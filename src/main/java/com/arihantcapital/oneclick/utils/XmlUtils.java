package com.arihantcapital.oneclick.utils;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import lombok.extern.slf4j.Slf4j;
import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.w3c.dom.Node;
import org.w3c.dom.NodeList;
import org.xml.sax.InputSource;

import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import java.io.StringReader;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Slf4j
public class XmlUtils {

    /**
     * Extract a single tag value from XML string using DOM parser
     *
     * @param xmlString The XML string to parse
     * @param tagName   The tag name to extract
     * @return Optional containing the tag value if found
     */
    public static Optional<String> extractTagValue(String xmlString, String tagName) {
        try {
            DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
            factory.setNamespaceAware(true);
            DocumentBuilder builder = factory.newDocumentBuilder();
            Document document = builder.parse(new InputSource(new StringReader(xmlString)));

            NodeList nodeList = document.getElementsByTagName(tagName);
            if (nodeList.getLength() > 0) {
                return Optional.of(nodeList.item(0).getTextContent());
            }
            return Optional.empty();
        } catch (Exception e) {
            log.error("Error extracting tag {} from XML: {}", tagName, e.getMessage());
            return Optional.empty();
        }
    }

    /**
     * Extract multiple tag values from XML string using DOM parser
     *
     * @param xmlString The XML string to parse
     * @param tagName   The tag name to extract
     * @return List of tag values found
     */
    public static List<String> extractTagValues(String xmlString, String tagName) {
        List<String> values = new ArrayList<>();
        try {
            DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
            factory.setNamespaceAware(true);
            DocumentBuilder builder = factory.newDocumentBuilder();
            Document document = builder.parse(new InputSource(new StringReader(xmlString)));

            NodeList nodeList = document.getElementsByTagName(tagName);
            for (int i = 0; i < nodeList.getLength(); i++) {
                values.add(nodeList.item(i).getTextContent());
            }
        } catch (Exception e) {
            log.error("Error extracting tags {} from XML: {}", tagName, e.getMessage());
        }
        return values;
    }

    /**
     * Extract tag value with namespace support
     *
     * @param xmlString    The XML string to parse
     * @param namespaceURI The namespace URI
     * @param tagName      The tag name to extract
     * @return Optional containing the tag value if found
     */
    public static Optional<String> extractTagValueWithNamespace(String xmlString, String namespaceURI, String tagName) {
        try {
            DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
            factory.setNamespaceAware(true);
            DocumentBuilder builder = factory.newDocumentBuilder();
            Document document = builder.parse(new InputSource(new StringReader(xmlString)));

            NodeList nodeList = document.getElementsByTagNameNS(namespaceURI, tagName);
            if (nodeList.getLength() > 0) {
                return Optional.of(nodeList.item(0).getTextContent());
            }
            return Optional.empty();
        } catch (Exception e) {
            log.error("Error extracting tag {} with namespace {} from XML: {}", tagName, namespaceURI, e.getMessage());
            return Optional.empty();
        }
    }

    /**
     * Extract attribute value from a specific tag
     *
     * @param xmlString     The XML string to parse
     * @param tagName       The tag name
     * @param attributeName The attribute name
     * @return Optional containing the attribute value if found
     */
    public static Optional<String> extractAttributeValue(String xmlString, String tagName, String attributeName) {
        try {
            DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
            factory.setNamespaceAware(true);
            DocumentBuilder builder = factory.newDocumentBuilder();
            Document document = builder.parse(new InputSource(new StringReader(xmlString)));

            NodeList nodeList = document.getElementsByTagName(tagName);
            if (nodeList.getLength() > 0) {
                Element element = (Element) nodeList.item(0);
                if (element.hasAttribute(attributeName)) {
                    return Optional.of(element.getAttribute(attributeName));
                }
            }
            return Optional.empty();
        } catch (Exception e) {
            log.error("Error extracting attribute {} from tag {} in XML: {}", attributeName, tagName, e.getMessage());
            return Optional.empty();
        }
    }

    /**
     * Extract nested tag value using XPath-like approach
     *
     * @param xmlString The XML string to parse
     * @param parentTag The parent tag name
     * @param childTag  The child tag name
     * @return Optional containing the child tag value if found
     */
    public static Optional<String> extractNestedTagValue(String xmlString, String parentTag, String childTag) {
        try {
            DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
            factory.setNamespaceAware(true);
            DocumentBuilder builder = factory.newDocumentBuilder();
            Document document = builder.parse(new InputSource(new StringReader(xmlString)));

            NodeList parentNodes = document.getElementsByTagName(parentTag);
            for (int i = 0; i < parentNodes.getLength(); i++) {
                Element parentElement = (Element) parentNodes.item(i);
                NodeList childNodes = parentElement.getElementsByTagName(childTag);
                if (childNodes.getLength() > 0) {
                    return Optional.of(childNodes.item(0).getTextContent());
                }
            }
            return Optional.empty();
        } catch (Exception e) {
            log.error("Error extracting nested tag {} from parent {} in XML: {}", childTag, parentTag, e.getMessage());
            return Optional.empty();
        }
    }

    /**
     * Simple regex-based extraction (for simple cases only)
     * WARNING: This is not recommended for complex XML as it doesn't handle nested
     * tags properly
     *
     * @param xmlString The XML string to parse
     * @param tagName   The tag name to extract
     * @return Optional containing the tag value if found
     */
    public static Optional<String> extractTagValueSimple(String xmlString, String tagName) {
        try {
            String pattern = "<" + tagName + ">(.*?)</" + tagName + ">";
            java.util.regex.Pattern regex = java.util.regex.Pattern.compile(pattern, java.util.regex.Pattern.DOTALL);
            java.util.regex.Matcher matcher = regex.matcher(xmlString);

            if (matcher.find()) {
                return Optional.of(matcher.group(1).trim());
            }
            return Optional.empty();
        } catch (Exception e) {
            log.error("Error extracting tag {} using regex from XML: {}", tagName, e.getMessage());
            return Optional.empty();
        }
    }

    /**
     * Extract all text content from a specific tag including nested content
     *
     * @param xmlString The XML string to parse
     * @param tagName   The tag name to extract
     * @return Optional containing the full tag content if found
     */
    public static Optional<String> extractFullTagContent(String xmlString, String tagName) {
        try {
            DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
            factory.setNamespaceAware(true);
            DocumentBuilder builder = factory.newDocumentBuilder();
            Document document = builder.parse(new InputSource(new StringReader(xmlString)));

            NodeList nodeList = document.getElementsByTagName(tagName);
            if (nodeList.getLength() > 0) {
                Node node = nodeList.item(0);
                StringBuilder content = new StringBuilder();
                NodeList children = node.getChildNodes();
                for (int i = 0; i < children.getLength(); i++) {
                    Node child = children.item(i);
                    if (child.getNodeType() == Node.TEXT_NODE) {
                        content.append(child.getTextContent());
                    } else if (child.getNodeType() == Node.ELEMENT_NODE) {
                        content.append(child.getTextContent());
                    }
                }
                return Optional.of(content.toString().trim());
            }
            return Optional.empty();
        } catch (Exception e) {
            log.error("Error extracting full content of tag {} from XML: {}", tagName, e.getMessage());
            return Optional.empty();
        }
    }

    /**
     * Recursively search for the first occurrence of a tag with the given name
     * This method traverses the entire XML tree to find the tag regardless of its
     * position
     *
     * @param xmlString The XML string to parse
     * @param tagName   The tag name to search for
     * @return Optional containing the tag value if found
     */
    public static Optional<String> findFirstTagRecursively(String xmlString, String tagName) {
        try {
            DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
            factory.setNamespaceAware(true);
            DocumentBuilder builder = factory.newDocumentBuilder();
            Document document = builder.parse(new InputSource(new StringReader(xmlString)));

            return findFirstTagRecursively(document.getDocumentElement(), tagName);
        } catch (Exception e) {
            log.error("Error finding tag {} recursively in XML: {}", tagName, e.getMessage());
            return Optional.empty();
        }
    }

    /**
     * Recursive helper method to search for a tag in the XML tree
     *
     * @param node    The current node to search
     * @param tagName The tag name to search for
     * @return Optional containing the tag value if found
     */
    private static Optional<String> findFirstTagRecursively(Node node, String tagName) {
        if (node == null) {
            return Optional.empty();
        }

        // Check if current node is the tag we're looking for
        if (node.getNodeType() == Node.ELEMENT_NODE) {
            Element element = (Element) node;
            String nodeName = element.getTagName();

            // Remove namespace prefix if present (e.g., "s:Body" -> "Body")
            String localName = nodeName.contains(":") ? nodeName.substring(nodeName.indexOf(":") + 1) : nodeName;

            if (localName.equals(tagName)) {
                return Optional.of(element.getTextContent());
            }
        }

        // Recursively search child nodes
        NodeList children = node.getChildNodes();
        for (int i = 0; i < children.getLength(); i++) {
            Optional<String> result = findFirstTagRecursively(children.item(i), tagName);
            if (result.isPresent()) {
                return result;
            }
        }

        return Optional.empty();
    }

    /**
     * Recursively search for the first occurrence of a tag with the given name
     * (case-insensitive)
     *
     * @param xmlString The XML string to parse
     * @param tagName   The tag name to search for (case-insensitive)
     * @return Optional containing the tag value if found
     */
    public static Optional<String> findFirstTagRecursivelyIgnoreCase(String xmlString, String tagName) {
        try {
            DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
            factory.setNamespaceAware(true);
            DocumentBuilder builder = factory.newDocumentBuilder();
            Document document = builder.parse(new InputSource(new StringReader(xmlString)));

            return findFirstTagRecursivelyIgnoreCase(document.getDocumentElement(), tagName);
        } catch (Exception e) {
            log.error("Error finding tag {} recursively (case-insensitive) in XML: {}", tagName, e.getMessage());
            return Optional.empty();
        }
    }

    /**
     * Recursive helper method to search for a tag in the XML tree
     * (case-insensitive)
     *
     * @param node    The current node to search
     * @param tagName The tag name to search for (case-insensitive)
     * @return Optional containing the tag value if found
     */
    private static Optional<String> findFirstTagRecursivelyIgnoreCase(Node node, String tagName) {
        if (node == null) {
            return Optional.empty();
        }

        // Check if current node is the tag we're looking for
        if (node.getNodeType() == Node.ELEMENT_NODE) {
            Element element = (Element) node;
            String nodeName = element.getTagName();

            // Remove namespace prefix if present (e.g., "s:Body" -> "Body")
            String localName = nodeName.contains(":") ? nodeName.substring(nodeName.indexOf(":") + 1) : nodeName;

            if (localName.equalsIgnoreCase(tagName)) {
                return Optional.of(element.getTextContent());
            }
        }

        // Recursively search child nodes
        NodeList children = node.getChildNodes();
        for (int i = 0; i < children.getLength(); i++) {
            Optional<String> result = findFirstTagRecursivelyIgnoreCase(children.item(i), tagName);
            if (result.isPresent()) {
                return result;
            }
        }

        return Optional.empty();
    }

    /**
     * Recursively search for all occurrences of a tag with the given name
     *
     * @param xmlString The XML string to parse
     * @param tagName   The tag name to search for
     * @return List of all tag values found
     */
    public static List<String> findAllTagsRecursively(String xmlString, String tagName) {
        List<String> results = new ArrayList<>();
        try {
            DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
            factory.setNamespaceAware(true);
            DocumentBuilder builder = factory.newDocumentBuilder();
            Document document = builder.parse(new InputSource(new StringReader(xmlString)));

            findAllTagsRecursively(document.getDocumentElement(), tagName, results);
        } catch (Exception e) {
            log.error("Error finding all tags {} recursively in XML: {}", tagName, e.getMessage());
        }
        return results;
    }

    /**
     * Recursive helper method to collect all occurrences of a tag in the XML tree
     *
     * @param node    The current node to search
     * @param tagName The tag name to search for
     * @param results List to collect the results
     */
    private static void findAllTagsRecursively(Node node, String tagName, List<String> results) {
        if (node == null) {
            return;
        }

        // Check if current node is the tag we're looking for
        if (node.getNodeType() == Node.ELEMENT_NODE) {
            Element element = (Element) node;
            String nodeName = element.getTagName();

            // Remove namespace prefix if present (e.g., "s:Body" -> "Body")
            String localName = nodeName.contains(":") ? nodeName.substring(nodeName.indexOf(":") + 1) : nodeName;

            if (localName.equals(tagName)) {
                results.add(element.getTextContent());
            }
        }

        // Recursively search child nodes
        NodeList children = node.getChildNodes();
        for (int i = 0; i < children.getLength(); i++) {
            findAllTagsRecursively(children.item(i), tagName, results);
        }
    }

    /**
     * Convert XML string to JsonNode
     *
     * @param xmlString The XML string to convert
     * @return JsonNode representation of the XML
     */
    public static JsonNode xmlToJsonNode(String xmlString) {
        try {
            DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
            factory.setNamespaceAware(true);
            DocumentBuilder builder = factory.newDocumentBuilder();
            Document document = builder.parse(new InputSource(new StringReader(xmlString)));

            ObjectMapper objectMapper = new ObjectMapper();
            return nodeToJsonNode(document.getDocumentElement(), objectMapper);
        } catch (Exception e) {
            log.error("Error converting XML to JsonNode: {}", e.getMessage());
            ObjectMapper objectMapper = new ObjectMapper();
            return objectMapper.createObjectNode().put("error", "Failed to parse XML: " + e.getMessage());
        }
    }

    /**
     * Convert XML string to JsonNode with custom ObjectMapper
     *
     * @param xmlString    The XML string to convert
     * @param objectMapper The ObjectMapper to use for conversion
     * @return JsonNode representation of the XML
     */
    public static JsonNode xmlToJsonNode(String xmlString, ObjectMapper objectMapper) {
        try {
            DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
            factory.setNamespaceAware(true);
            DocumentBuilder builder = factory.newDocumentBuilder();
            Document document = builder.parse(new InputSource(new StringReader(xmlString)));

            return nodeToJsonNode(document.getDocumentElement(), objectMapper);
        } catch (Exception e) {
            log.error("Error converting XML to JsonNode: {}", e.getMessage());
            return objectMapper.createObjectNode().put("error", "Failed to parse XML: " + e.getMessage());
        }
    }

    /**
     * Convert XML Node to JsonNode recursively
     *
     * @param node         The XML Node to convert
     * @param objectMapper The ObjectMapper to use
     * @return JsonNode representation of the XML Node
     */
    private static JsonNode nodeToJsonNode(Node node, ObjectMapper objectMapper) {
        if (node == null) {
            return objectMapper.nullNode();
        }

        switch (node.getNodeType()) {
            case Node.DOCUMENT_NODE:
            case Node.ELEMENT_NODE:
                return elementToJsonNode((Element) node, objectMapper);
            case Node.TEXT_NODE:
            case Node.CDATA_SECTION_NODE:
                String text = node.getTextContent().trim();
                if (text.isEmpty()) {
                    return objectMapper.nullNode();
                }
                // Try to convert to number if possible
                try {
                    if (text.contains(".")) {
                        return objectMapper.valueToTree(Double.parseDouble(text));
                    } else {
                        return objectMapper.valueToTree(Long.parseLong(text));
                    }
                } catch (NumberFormatException e) {
                    return objectMapper.valueToTree(text);
                }
            case Node.ATTRIBUTE_NODE:
                return objectMapper.valueToTree(node.getNodeValue());
            default:
                return objectMapper.nullNode();
        }
    }

    /**
     * Convert XML Element to JsonNode
     *
     * @param element      The XML Element to convert
     * @param objectMapper The ObjectMapper to use
     * @return JsonNode representation of the XML Element
     */
    private static JsonNode elementToJsonNode(Element element, ObjectMapper objectMapper) {
        ObjectNode jsonNode = objectMapper.createObjectNode();

        // Handle attributes
        if (element.hasAttributes()) {
            ObjectNode attributesNode = objectMapper.createObjectNode();
            for (int i = 0; i < element.getAttributes().getLength(); i++) {
                Node attr = element.getAttributes().item(i);
                attributesNode.put(attr.getNodeName(), attr.getNodeValue());
            }
            jsonNode.set("@attributes", attributesNode);
        }

        // Handle child nodes
        NodeList children = element.getChildNodes();
        if (children.getLength() == 0) {
            // Empty element
            return jsonNode;
        }

        // Check if element has only text content
        if (children.getLength() == 1 && children.item(0).getNodeType() == Node.TEXT_NODE) {
            String text = children.item(0).getTextContent().trim();
            if (!text.isEmpty()) {
                try {
                    if (text.contains(".")) {
                        jsonNode.put("#text", Double.parseDouble(text));
                    } else {
                        jsonNode.put("#text", Long.parseLong(text));
                    }
                } catch (NumberFormatException e) {
                    jsonNode.put("#text", text);
                }
            }
            return jsonNode;
        }

        // Handle mixed content or multiple child elements
        for (int i = 0; i < children.getLength(); i++) {
            Node child = children.item(i);
            if (child.getNodeType() == Node.ELEMENT_NODE) {
                Element childElement = (Element) child;
                String childName = childElement.getTagName();

                // Remove namespace prefix if present
                String localName = childName.contains(":") ? childName.substring(childName.indexOf(":") + 1)
                        : childName;

                JsonNode childJson = nodeToJsonNode(childElement, objectMapper);

                // Check if this element name already exists (multiple children with same name)
                if (jsonNode.has(localName)) {
                    JsonNode existingNode = jsonNode.get(localName);
                    if (existingNode.isArray()) {
                        ((ArrayNode) existingNode).add(childJson);
                    } else {
                        ArrayNode arrayNode = objectMapper.createArrayNode();
                        arrayNode.add(existingNode);
                        arrayNode.add(childJson);
                        jsonNode.set(localName, arrayNode);
                    }
                } else {
                    jsonNode.set(localName, childJson);
                }
            } else if (child.getNodeType() == Node.TEXT_NODE) {
                String text = child.getTextContent().trim();
                if (!text.isEmpty()) {
                    jsonNode.put("#text", text);
                }
            }
        }

        return jsonNode;
    }

    /**
     * Convert XML string to JsonNode with simplified structure (flattened)
     * This method creates a simpler JSON structure without attributes and mixed
     * content handling
     *
     * @param xmlString The XML string to convert
     * @return Simplified JsonNode representation of the XML
     */
    public static JsonNode xmlToSimpleJsonNode(String xmlString) {
        try {
            DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
            factory.setNamespaceAware(true);
            DocumentBuilder builder = factory.newDocumentBuilder();
            Document document = builder.parse(new InputSource(new StringReader(xmlString)));

            ObjectMapper objectMapper = new ObjectMapper();
            return elementToSimpleJsonNode(document.getDocumentElement(), objectMapper);
        } catch (Exception e) {
            log.error("Error converting XML to simple JsonNode: {}", e.getMessage());
            ObjectMapper objectMapper = new ObjectMapper();
            return objectMapper.createObjectNode().put("error", "Failed to parse XML: " + e.getMessage());
        }
    }

    /**
     * Convert XML Element to simplified JsonNode
     *
     * @param element      The XML Element to convert
     * @param objectMapper The ObjectMapper to use
     * @return Simplified JsonNode representation of the XML Element
     */
    private static JsonNode elementToSimpleJsonNode(Element element, ObjectMapper objectMapper) {
        ObjectNode jsonNode = objectMapper.createObjectNode();

        NodeList children = element.getChildNodes();
        if (children.getLength() == 0) {
            return jsonNode;
        }

        // Check if element has only text content
        if (children.getLength() == 1 && children.item(0).getNodeType() == Node.TEXT_NODE) {
            String text = children.item(0).getTextContent().trim();
            if (!text.isEmpty()) {
                try {
                    if (text.contains(".")) {
                        return objectMapper.valueToTree(Double.parseDouble(text));
                    } else {
                        return objectMapper.valueToTree(Long.parseLong(text));
                    }
                } catch (NumberFormatException e) {
                    return objectMapper.valueToTree(text);
                }
            }
            return jsonNode;
        }

        // Handle child elements
        for (int i = 0; i < children.getLength(); i++) {
            Node child = children.item(i);
            if (child.getNodeType() == Node.ELEMENT_NODE) {
                Element childElement = (Element) child;
                String childName = childElement.getTagName();

                // Remove namespace prefix if present
                String localName = childName.contains(":") ? childName.substring(childName.indexOf(":") + 1)
                        : childName;

                JsonNode childJson = elementToSimpleJsonNode(childElement, objectMapper);

                // Check if this element name already exists (multiple children with same name)
                if (jsonNode.has(localName)) {
                    JsonNode existingNode = jsonNode.get(localName);
                    if (existingNode.isArray()) {
                        ((ArrayNode) existingNode).add(childJson);
                    } else {
                        ArrayNode arrayNode = objectMapper.createArrayNode();
                        arrayNode.add(existingNode);
                        arrayNode.add(childJson);
                        jsonNode.set(localName, arrayNode);
                    }
                } else {
                    jsonNode.set(localName, childJson);
                }
            }
        }

        return jsonNode;
    }

    /**
     * Extract specific tag value and convert to JsonNode
     *
     * @param xmlString The XML string to parse
     * @param tagName   The tag name to extract
     * @return JsonNode containing the tag value
     */
    public static JsonNode extractTagAsJsonNode(String xmlString, String tagName) {
        ObjectMapper objectMapper = new ObjectMapper();
        Optional<String> tagValue = extractTagValue(xmlString, tagName);

        if (tagValue.isPresent()) {
            String value = tagValue.get();
            try {
                if (value.contains(".")) {
                    return objectMapper.valueToTree(Double.parseDouble(value));
                } else {
                    return objectMapper.valueToTree(Long.parseLong(value));
                }
            } catch (NumberFormatException e) {
                return objectMapper.valueToTree(value);
            }
        }

        return objectMapper.nullNode();
    }

    /**
     * Extract multiple tag values and convert to JsonNode array
     *
     * @param xmlString The XML string to parse
     * @param tagName   The tag name to extract
     * @return JsonNode array containing the tag values
     */
    public static JsonNode extractTagsAsJsonNodeArray(String xmlString, String tagName) {
        ObjectMapper objectMapper = new ObjectMapper();
        List<String> tagValues = extractTagValues(xmlString, tagName);

        ArrayNode arrayNode = objectMapper.createArrayNode();
        for (String value : tagValues) {
            try {
                if (value.contains(".")) {
                    arrayNode.add(Double.parseDouble(value));
                } else {
                    arrayNode.add(Long.parseLong(value));
                }
            } catch (NumberFormatException e) {
                arrayNode.add(value);
            }
        }

        return arrayNode;
    }

    /**
     * Extract nested XML structure recursively and convert to JsonNode
     * This method handles complex nested structures with multiple levels
     *
     * @param xmlString The XML string to parse
     * @param rootTag   The root tag to start extraction from
     * @return JsonNode containing the nested structure
     */
    public static JsonNode extractNestedStructureAsJsonNode(String xmlString, String rootTag) {
        try {
            DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
            factory.setNamespaceAware(true);
            DocumentBuilder builder = factory.newDocumentBuilder();
            Document document = builder.parse(new InputSource(new StringReader(xmlString)));

            NodeList rootNodes = document.getElementsByTagName(rootTag);
            if (rootNodes.getLength() > 0) {
                ObjectMapper objectMapper = new ObjectMapper();
                return elementToJsonNode((Element) rootNodes.item(0), objectMapper);
            }

            return new ObjectMapper().createObjectNode();
        } catch (Exception e) {
            log.error("Error extracting nested structure for tag {} from XML: {}", rootTag, e.getMessage());
            return new ObjectMapper().createObjectNode().put("error",
                    "Failed to extract nested structure: " + e.getMessage());
        }
    }

    /**
     * Flatten nested XML structure recursively into a simple key-value JsonNode
     * This creates a flat structure where nested elements are represented as
     * dot-separated keys
     *
     * @param xmlString The XML string to flatten
     * @return Flattened JsonNode with dot-separated keys
     */
    public static JsonNode flattenXmlToJsonNode(String xmlString) {
        try {
            DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
            factory.setNamespaceAware(true);
            DocumentBuilder builder = factory.newDocumentBuilder();
            Document document = builder.parse(new InputSource(new StringReader(xmlString)));

            ObjectMapper objectMapper = new ObjectMapper();
            ObjectNode flattenedNode = objectMapper.createObjectNode();

            flattenElementRecursively(document.getDocumentElement(), "", flattenedNode, objectMapper);
            return flattenedNode;
        } catch (Exception e) {
            log.error("Error flattening XML to JsonNode: {}", e.getMessage());
            ObjectMapper objectMapper = new ObjectMapper();
            return objectMapper.createObjectNode().put("error", "Failed to flatten XML: " + e.getMessage());
        }
    }

    /**
     * Recursively flatten XML element into key-value pairs
     *
     * @param element       The XML element to flatten
     * @param prefix        The current key prefix
     * @param flattenedNode The JsonNode to add flattened values to
     * @param objectMapper  The ObjectMapper to use
     */
    private static void flattenElementRecursively(Element element, String prefix, ObjectNode flattenedNode,
            ObjectMapper objectMapper) {
        String elementName = element.getTagName();
        // Remove namespace prefix if present
        String localName = elementName.contains(":") ? elementName.substring(elementName.indexOf(":") + 1)
                : elementName;

        String currentKey = prefix.isEmpty() ? localName : prefix + "." + localName;

        // Handle attributes
        if (element.hasAttributes()) {
            for (int i = 0; i < element.getAttributes().getLength(); i++) {
                Node attr = element.getAttributes().item(i);
                String attrKey = currentKey + ".@" + attr.getNodeName();
                flattenedNode.put(attrKey, attr.getNodeValue());
            }
        }

        NodeList children = element.getChildNodes();
        if (children.getLength() == 0) {
            // Empty element
            flattenedNode.put(currentKey, "");
            return;
        }

        // Check if element has only text content
        if (children.getLength() == 1 && children.item(0).getNodeType() == Node.TEXT_NODE) {
            String text = children.item(0).getTextContent().trim();
            if (!text.isEmpty()) {
                // Check for boolean values first
                if ("true".equalsIgnoreCase(text) || "false".equalsIgnoreCase(text)) {
                    flattenedNode.put(currentKey, Boolean.parseBoolean(text));
                } else {
                    // Treat everything else as string
                    flattenedNode.put(currentKey, text);
                }
            } else {
                flattenedNode.put(currentKey, "");
            }
            return;
        }

        // Handle child elements recursively
        for (int i = 0; i < children.getLength(); i++) {
            Node child = children.item(i);
            if (child.getNodeType() == Node.ELEMENT_NODE) {
                flattenElementRecursively((Element) child, currentKey, flattenedNode, objectMapper);
            } else if (child.getNodeType() == Node.TEXT_NODE) {
                String text = child.getTextContent().trim();
                if (!text.isEmpty()) {
                    flattenedNode.put(currentKey + ".text", text);
                }
            }
        }
    }

    /**
     * Extract nested XML structure with custom path separator
     *
     * @param xmlString     The XML string to parse
     * @param pathSeparator The separator to use for nested paths (default: ".")
     * @return JsonNode with custom path separator
     */
    public static JsonNode flattenXmlToJsonNode(String xmlString, String pathSeparator) {
        try {
            DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
            factory.setNamespaceAware(true);
            DocumentBuilder builder = factory.newDocumentBuilder();
            Document document = builder.parse(new InputSource(new StringReader(xmlString)));

            ObjectMapper objectMapper = new ObjectMapper();
            ObjectNode flattenedNode = objectMapper.createObjectNode();

            flattenElementRecursivelyWithSeparator(document.getDocumentElement(), "", flattenedNode, objectMapper,
                    pathSeparator);
            return flattenedNode;
        } catch (Exception e) {
            log.error("Error flattening XML to JsonNode with custom separator: {}", e.getMessage());
            ObjectMapper objectMapper = new ObjectMapper();
            return objectMapper.createObjectNode().put("error", "Failed to flatten XML: " + e.getMessage());
        }
    }

    /**
     * Recursively flatten XML element with custom path separator
     *
     * @param element       The XML element to flatten
     * @param prefix        The current key prefix
     * @param flattenedNode The JsonNode to add flattened values to
     * @param objectMapper  The ObjectMapper to use
     * @param pathSeparator The custom path separator
     */
    private static void flattenElementRecursivelyWithSeparator(Element element, String prefix, ObjectNode flattenedNode,
            ObjectMapper objectMapper, String pathSeparator) {
        String elementName = element.getTagName();
        String localName = elementName.contains(":") ? elementName.substring(elementName.indexOf(":") + 1)
                : elementName;

        String currentKey = prefix.isEmpty() ? localName : prefix + pathSeparator + localName;

        // Handle attributes
        if (element.hasAttributes()) {
            for (int i = 0; i < element.getAttributes().getLength(); i++) {
                Node attr = element.getAttributes().item(i);
                String attrKey = currentKey + pathSeparator + "@" + attr.getNodeName();
                flattenedNode.put(attrKey, attr.getNodeValue());
            }
        }

        NodeList children = element.getChildNodes();
        if (children.getLength() == 0) {
            flattenedNode.put(currentKey, "");
            return;
        }

        // Check if element has only text content
        if (children.getLength() == 1 && children.item(0).getNodeType() == Node.TEXT_NODE) {
            String text = children.item(0).getTextContent().trim();
            if (!text.isEmpty()) {
                // Check for boolean values first
                if ("true".equalsIgnoreCase(text) || "false".equalsIgnoreCase(text)) {
                    flattenedNode.put(currentKey, Boolean.parseBoolean(text));
                } else {
                    // Treat everything else as string
                    flattenedNode.put(currentKey, text);
                }
            } else {
                flattenedNode.put(currentKey, "");
            }
            return;
        }

        // Handle child elements recursively
        for (int i = 0; i < children.getLength(); i++) {
            Node child = children.item(i);
            if (child.getNodeType() == Node.ELEMENT_NODE) {
                flattenElementRecursivelyWithSeparator((Element) child, currentKey, flattenedNode, objectMapper,
                        pathSeparator);
            } else if (child.getNodeType() == Node.TEXT_NODE) {
                String text = child.getTextContent().trim();
                if (!text.isEmpty()) {
                    flattenedNode.put(currentKey + pathSeparator + "text", text);
                }
            }
        }
    }

    /**
     * Extract nested XML structure recursively with depth limit
     *
     * @param xmlString The XML string to parse
     * @param maxDepth  The maximum depth to traverse
     * @return JsonNode containing the nested structure up to maxDepth
     */
    public static JsonNode extractNestedStructureWithDepthLimit(String xmlString, int maxDepth) {
        try {
            DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
            factory.setNamespaceAware(true);
            DocumentBuilder builder = factory.newDocumentBuilder();
            Document document = builder.parse(new InputSource(new StringReader(xmlString)));

            ObjectMapper objectMapper = new ObjectMapper();
            return elementToJsonNodeWithDepthLimit(document.getDocumentElement(), objectMapper, 0, maxDepth);
        } catch (Exception e) {
            log.error("Error extracting nested structure with depth limit from XML: {}", e.getMessage());
            ObjectMapper objectMapper = new ObjectMapper();
            return objectMapper.createObjectNode().put("error",
                    "Failed to extract nested structure: " + e.getMessage());
        }
    }

    /**
     * Convert XML Element to JsonNode with depth limit
     *
     * @param element      The XML Element to convert
     * @param objectMapper The ObjectMapper to use
     * @param currentDepth The current depth level
     * @param maxDepth     The maximum depth to traverse
     * @return JsonNode representation of the XML Element
     */
    private static JsonNode elementToJsonNodeWithDepthLimit(Element element, ObjectMapper objectMapper,
            int currentDepth, int maxDepth) {
        if (currentDepth >= maxDepth) {
            return objectMapper.valueToTree(element.getTextContent().trim());
        }

        ObjectNode jsonNode = objectMapper.createObjectNode();

        // Handle attributes
        if (element.hasAttributes()) {
            ObjectNode attributesNode = objectMapper.createObjectNode();
            for (int i = 0; i < element.getAttributes().getLength(); i++) {
                Node attr = element.getAttributes().item(i);
                attributesNode.put(attr.getNodeName(), attr.getNodeValue());
            }
            jsonNode.set("@attributes", attributesNode);
        }

        NodeList children = element.getChildNodes();
        if (children.getLength() == 0) {
            return jsonNode;
        }

        // Check if element has only text content
        if (children.getLength() == 1 && children.item(0).getNodeType() == Node.TEXT_NODE) {
            String text = children.item(0).getTextContent().trim();
            if (!text.isEmpty()) {
                // Check for boolean values first
                if ("true".equalsIgnoreCase(text) || "false".equalsIgnoreCase(text)) {
                    jsonNode.put("#text", Boolean.parseBoolean(text));
                } else {
                    // Treat everything else as string
                    jsonNode.put("#text", text);
                }
            }
            return jsonNode;
        }

        // Handle child elements recursively with depth limit
        for (int i = 0; i < children.getLength(); i++) {
            Node child = children.item(i);
            if (child.getNodeType() == Node.ELEMENT_NODE) {
                Element childElement = (Element) child;
                String childName = childElement.getTagName();
                String localName = childName.contains(":") ? childName.substring(childName.indexOf(":") + 1)
                        : childName;

                JsonNode childJson = elementToJsonNodeWithDepthLimit(childElement, objectMapper, currentDepth + 1,
                        maxDepth);

                // Check if this element name already exists
                if (jsonNode.has(localName)) {
                    JsonNode existingNode = jsonNode.get(localName);
                    if (existingNode.isArray()) {
                        ((ArrayNode) existingNode).add(childJson);
                    } else {
                        ArrayNode arrayNode = objectMapper.createArrayNode();
                        arrayNode.add(existingNode);
                        arrayNode.add(childJson);
                        jsonNode.set(localName, arrayNode);
                    }
                } else {
                    jsonNode.set(localName, childJson);
                }
            } else if (child.getNodeType() == Node.TEXT_NODE) {
                String text = child.getTextContent().trim();
                if (!text.isEmpty()) {
                    jsonNode.put("#text", text);
                }
            }
        }

        return jsonNode;
    }

    /**
     * Extract nested XML structure recursively and return as Map
     * This method provides an alternative to JsonNode for easier manipulation
     *
     * @param xmlString The XML string to parse
     * @return Map containing the nested structure
     */
    public static java.util.Map<String, Object> extractNestedStructureAsMap(String xmlString) {
        try {
            DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
            factory.setNamespaceAware(true);
            DocumentBuilder builder = factory.newDocumentBuilder();
            Document document = builder.parse(new InputSource(new StringReader(xmlString)));

            return elementToMapRecursively(document.getDocumentElement());
        } catch (Exception e) {
            log.error("Error extracting nested structure as Map from XML: {}", e.getMessage());
            java.util.Map<String, Object> errorMap = new java.util.HashMap<>();
            errorMap.put("error", "Failed to extract nested structure: " + e.getMessage());
            return errorMap;
        }
    }

    /**
     * Convert XML Element to Map recursively
     *
     * @param element The XML Element to convert
     * @return Map representation of the XML Element
     */
    private static java.util.Map<String, Object> elementToMapRecursively(Element element) {
        java.util.Map<String, Object> map = new java.util.HashMap<>();

        // Handle attributes
        if (element.hasAttributes()) {
            java.util.Map<String, String> attributes = new java.util.HashMap<>();
            for (int i = 0; i < element.getAttributes().getLength(); i++) {
                Node attr = element.getAttributes().item(i);
                attributes.put(attr.getNodeName(), attr.getNodeValue());
            }
            map.put("@attributes", attributes);
        }

        NodeList children = element.getChildNodes();
        if (children.getLength() == 0) {
            return map;
        }

        // Check if element has only text content
        if (children.getLength() == 1 && children.item(0).getNodeType() == Node.TEXT_NODE) {
            String text = children.item(0).getTextContent().trim();
            if (!text.isEmpty()) {
                try {
                    if (text.contains(".")) {
                        map.put("#text", Double.parseDouble(text));
                    } else {
                        map.put("#text", Long.parseLong(text));
                    }
                } catch (NumberFormatException e) {
                    map.put("#text", text);
                }
            }
            return map;
        }

        // Handle child elements recursively
        for (int i = 0; i < children.getLength(); i++) {
            Node child = children.item(i);
            if (child.getNodeType() == Node.ELEMENT_NODE) {
                Element childElement = (Element) child;
                String childName = childElement.getTagName();
                String localName = childName.contains(":") ? childName.substring(childName.indexOf(":") + 1)
                        : childName;

                java.util.Map<String, Object> childMap = elementToMapRecursively(childElement);

                // Check if this element name already exists
                if (map.containsKey(localName)) {
                    Object existingValue = map.get(localName);
                    if (existingValue instanceof List) {
                        ((List<Object>) existingValue).add(childMap);
                    } else {
                        List<Object> list = new ArrayList<>();
                        list.add(existingValue);
                        list.add(childMap);
                        map.put(localName, list);
                    }
                } else {
                    map.put(localName, childMap);
                }
            } else if (child.getNodeType() == Node.TEXT_NODE) {
                String text = child.getTextContent().trim();
                if (!text.isEmpty()) {
                    map.put("#text", text);
                }
            }
        }

        return map;
    }

    /**
     * Extract nested XML structure recursively and convert to JsonNode without
     * #text wrapper
     * This method provides cleaner output by directly using text values instead of
     * #text nodes
     * 
     * @param xmlString The XML string to parse
     * @param rootTag   The root tag to start extraction from
     * @return JsonNode containing the nested structure without #text wrapper
     */
    public static JsonNode extractNestedStructureAsJsonNodeClean(String xmlString, String rootTag) {
        try {
            DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
            factory.setNamespaceAware(true);
            DocumentBuilder builder = factory.newDocumentBuilder();
            Document document = builder.parse(new InputSource(new StringReader(xmlString)));

            NodeList rootNodes = document.getElementsByTagName(rootTag);
            if (rootNodes.getLength() > 0) {
                ObjectMapper objectMapper = new ObjectMapper();
                return elementToJsonNodeClean((Element) rootNodes.item(0), objectMapper);
            }

            return new ObjectMapper().createObjectNode();
        } catch (Exception e) {
            log.error("Error extracting nested structure for tag {} from XML: {}", rootTag, e.getMessage());
            return new ObjectMapper().createObjectNode().put("error",
                    "Failed to extract nested structure: " + e.getMessage());
        }
    }

    /**
     * Convert XML Element to JsonNode without #text wrapper
     * 
     * @param element      The XML Element to convert
     * @param objectMapper The ObjectMapper to use
     * @return JsonNode representation of the XML Element without #text wrapper
     */
    private static JsonNode elementToJsonNodeClean(Element element, ObjectMapper objectMapper) {
        // If the element has no attributes and no children, or only whitespace, return
        // empty string
        // boolean hasAttributes = element.hasAttributes() &&
        // element.getAttributes().getLength() > 0;
        NodeList children = element.getChildNodes();
        boolean hasNonEmptyText = false;
        for (int i = 0; i < children.getLength(); i++) {
            Node child = children.item(i);
            if (child.getNodeType() == Node.TEXT_NODE && !child.getTextContent().trim().isEmpty()) {
                hasNonEmptyText = true;
                break;
            }
            if (child.getNodeType() == Node.ELEMENT_NODE) {
                hasNonEmptyText = true;
                break;
            }
        }
        if (!hasNonEmptyText) {
            return objectMapper.valueToTree("");
        }

        ObjectNode jsonNode = objectMapper.createObjectNode();

        // Handle attributes
        // if (hasAttributes) {
        // ObjectNode attributesNode = objectMapper.createObjectNode();
        // for (int i = 0; i < element.getAttributes().getLength(); i++) {
        // Node attr = element.getAttributes().item(i);
        // attributesNode.put(attr.getNodeName(), attr.getNodeValue());
        // }
        // jsonNode.set("@attributes", attributesNode);
        // }

        if (children.getLength() == 0) {
            // Empty element (should not reach here due to above check)
            return jsonNode;
        }

        // Check if element has only text content
        if (children.getLength() == 1 && children.item(0).getNodeType() == Node.TEXT_NODE) {
            String text = children.item(0).getTextContent().trim();
            if (!text.isEmpty()) {
                // Check for boolean values first
                if ("true".equalsIgnoreCase(text) || "false".equalsIgnoreCase(text)) {
                    return objectMapper.valueToTree(Boolean.parseBoolean(text));
                }
                // Treat everything else as string
                return objectMapper.valueToTree(text);
            }
            return objectMapper.valueToTree("");
        }

        // Handle mixed content or multiple child elements
        for (int i = 0; i < children.getLength(); i++) {
            Node child = children.item(i);
            if (child.getNodeType() == Node.ELEMENT_NODE) {
                Element childElement = (Element) child;
                String childName = childElement.getTagName();

                // Remove namespace prefix if present
                String localName = childName.contains(":") ? childName.substring(childName.indexOf(":") + 1)
                        : childName;

                JsonNode childJson = elementToJsonNodeClean(childElement, objectMapper);

                // Check if this element name already exists (multiple children with same name)
                if (jsonNode.has(localName)) {
                    JsonNode existingNode = jsonNode.get(localName);
                    if (existingNode.isArray()) {
                        ((ArrayNode) existingNode).add(childJson);
                    } else {
                        ArrayNode arrayNode = objectMapper.createArrayNode();
                        arrayNode.add(existingNode);
                        arrayNode.add(childJson);
                        jsonNode.set(localName, arrayNode);
                    }
                } else {
                    jsonNode.set(localName, childJson);
                }
            } else if (child.getNodeType() == Node.TEXT_NODE) {
                String text = child.getTextContent().trim();
                if (!text.isEmpty()) {
                    jsonNode.put("#text", text);
                }
            }
        }

        return jsonNode;
    }

    /**
     * Convert XML string to JsonNode without #text wrapper
     * This method provides cleaner output by directly using text values instead of
     * #text nodes
     * 
     * @param xmlString The XML string to convert
     * @return JsonNode representation of the XML without #text wrapper
     */
    public static JsonNode xmlToJsonNodeClean(String xmlString) {
        try {
            DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
            factory.setNamespaceAware(true);
            DocumentBuilder builder = factory.newDocumentBuilder();
            Document document = builder.parse(new InputSource(new StringReader(xmlString)));

            ObjectMapper objectMapper = new ObjectMapper();
            return elementToJsonNodeClean(document.getDocumentElement(), objectMapper);
        } catch (Exception e) {
            log.error("Error converting XML to clean JsonNode: {}", e.getMessage());
            ObjectMapper objectMapper = new ObjectMapper();
            return objectMapper.createObjectNode().put("error", "Failed to parse XML: " + e.getMessage());
        }
    }

    /**
     * Convert XML string to JsonNode without #text wrapper with custom ObjectMapper
     * 
     * @param xmlString    The XML string to convert
     * @param objectMapper The ObjectMapper to use for conversion
     * @return JsonNode representation of the XML without #text wrapper
     */
    public static JsonNode xmlToJsonNodeClean(String xmlString, ObjectMapper objectMapper) {
        try {
            DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
            factory.setNamespaceAware(true);
            DocumentBuilder builder = factory.newDocumentBuilder();
            Document document = builder.parse(new InputSource(new StringReader(xmlString)));

            return elementToJsonNodeClean(document.getDocumentElement(), objectMapper);
        } catch (Exception e) {
            log.error("Error converting XML to clean JsonNode: {}", e.getMessage());
            return objectMapper.createObjectNode().put("error", "Failed to parse XML: " + e.getMessage());
        }
    }
}