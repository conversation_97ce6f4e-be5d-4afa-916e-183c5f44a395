package com.arihantcapital.oneclick.core.dto.ckyc;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotEmpty;
import lombok.Getter;

@Getter
@JsonInclude(JsonInclude.Include.NON_NULL)
public class CkycVerifyRQ {

    @JsonProperty(value = "requestId")
    private String requestId;

    @NotEmpty
    @JsonProperty(value = "idType")
    private String idType;

    @NotEmpty
    @JsonProperty(value = "idNumber")
    private String idNumber;
}


