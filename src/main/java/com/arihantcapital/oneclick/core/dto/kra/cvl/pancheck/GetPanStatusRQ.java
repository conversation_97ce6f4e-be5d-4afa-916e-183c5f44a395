package com.arihantcapital.oneclick.core.dto.kra.cvl.pancheck;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import lombok.Data;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class GetPanStatusRQ {

    @NotBlank
    @Pattern(regexp = "^[A-Z]{3}[ABCFGHLPT][A-Z][0-9]{4}[A-Z]$")
    @JsonProperty(value = "panNumber")
    private String panNumber;

}