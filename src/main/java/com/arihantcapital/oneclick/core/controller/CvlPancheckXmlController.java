package com.arihantcapital.oneclick.core.controller;

import com.arihantcapital.oneclick.core.dto.kra.cvl.pancheck.GetKraRQ;
import com.arihantcapital.oneclick.core.dto.kra.cvl.pancheck.GetPanStatusRQ;
import com.arihantcapital.oneclick.core.service.CvlPancheckXmlService;
import com.fasterxml.jackson.databind.JsonNode;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping(value = "/api/kra/cvl/pancheck/xml")
@RequiredArgsConstructor
public class CvlPancheckXmlController {

    private final CvlPancheckXmlService cvlPancheckXmlService;

    @GetMapping(value = "/get-token")
    public ResponseEntity<JsonNode> getToken(@RequestParam(value = "passKey", required = false) String passKey) {
        return ResponseEntity.ok(cvlPancheckXmlService.getToken(passKey));
    }

    @PostMapping(value = "/get-pan-status")
    public ResponseEntity<JsonNode> getPanStatus(@Valid @RequestBody GetPanStatusRQ getPanStatusRQ) {
        return ResponseEntity.ok(cvlPancheckXmlService.getPanStatus(getPanStatusRQ.getPanNumber()));
    }

    @PostMapping(value = "/solicit-pan-details-fetch-all-kra")
    public ResponseEntity<JsonNode> solicitPANDetailsFetchALLKRA(@Valid @RequestBody GetKraRQ getKraRQ) {
        return ResponseEntity.ok(cvlPancheckXmlService.solicitPANDetailsFetchALLKRA(getKraRQ.getPanNumber(), getKraRQ.getKraCode(), getKraRQ.getDob(), getKraRQ.getFetchType()));
    }

    @PostMapping(value = "/insert-update-kyc-record")
    public ResponseEntity<JsonNode> insertUpdateKYCRecord() {
        return ResponseEntity.ok(cvlPancheckXmlService.insertUpdateKYCRecord(null));
    }
}
