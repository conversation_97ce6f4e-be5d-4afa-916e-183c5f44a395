package com.arihantcapital.oneclick.core.controller;

import com.arihantcapital.oneclick.core.service.MsilService;
import com.fasterxml.jackson.databind.JsonNode;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping(value = "/api/msil")
@RequiredArgsConstructor
public class MsilController {

    private final MsilService msilService;

    @PostMapping(value = "/insert-client-details-bulk")
    public ResponseEntity<String> insertClientDetails(@RequestBody List<JsonNode> users) {
        return ResponseEntity.ok(msilService.insertClientDetails(users));
    }

    @PostMapping(value = "/insert-client-details")
    public ResponseEntity<String> insertClientDetails(@RequestBody JsonNode user) {
        return ResponseEntity.ok(msilService.insertClientDetails(user));
    }

    @PostMapping(value = "/update-client-details-bulk")
    public ResponseEntity<String> updateClientDetails(@RequestBody List<JsonNode> users) {
        return ResponseEntity.ok(msilService.updateClientDetails(users));
    }

    @PostMapping(value = "/update-client-details")
    public ResponseEntity<String> updateClientDetails(@RequestBody JsonNode user) {
        return ResponseEntity.ok(msilService.updateClientDetails(user));
    }
}
