package com.arihantcapital.oneclick.core.client.nse.ucc;


import com.arihantcapital.oneclick.OneclickProperties;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.HashMap;
import java.util.Map;

@Slf4j
@Service(value = "nseUccClient")
public class NseUccClientImpl {

    private final RestTemplate restTemplate = new RestTemplate();
    private final ObjectMapper objectMapper = new ObjectMapper();
    private NseUccClientImpl.ClientConfig clientConfig = new ClientConfig();

    public NseUccClientImpl(NseUccClientImpl.ClientConfig clientConfig) {
        this.clientConfig = clientConfig;
        log.info("NSE UCC Client Config: {}", clientConfig);
    }

    public NseUccClientImpl() {
    }

    public String authenticate() throws JsonProcessingException {
        String route = ClientRoutes.AUTHENTICATE;
        String url = clientConfig.getBaseUrl() + route;

        // Http Headers
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.setContentType(MediaType.APPLICATION_JSON);

        // Request Payload
        Map<String, String> payload = new HashMap<>();
        payload.put("username", clientConfig.getUsername());
        payload.put("password", clientConfig.getPassword());
        payload.put("apiName", clientConfig.getApiName());

        log.info("NSE UCC Authenticate Request Headers: {}", httpHeaders);
        log.info("NSE UCC Authenticate Request Body: {}", payload);

        // Sending Request
        HttpEntity<String> requestEntity = new HttpEntity<>(objectMapper.writeValueAsString(payload), httpHeaders);
        ResponseEntity<String> response = restTemplate.postForEntity(url, requestEntity, String.class);
        String responseString = response.getBody();
        log.info("NSE UCC Authenticate Response: {}", responseString);

        return "";
    }

    public String clientUpload(String token, JsonNode payload) throws JsonProcessingException {
        String route = ClientRoutes.CLIENT_UPLOAD;
        String url = clientConfig.getBaseUrl() + route;
        log.info("NSE UCC Client Upload URL: {}", url);

        // Fetching token from database, else fetching from api
        String authToken = StringUtils.isBlank(token) ? this.authenticate() : token;

        // Http Headers
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.setContentType(MediaType.APPLICATION_JSON);
        httpHeaders.add("Authorization", "Bearer " + authToken);

        // Request Payload
        Map<String, Object> body = new HashMap<>();
        body.put("request", payload);

        log.info("NSE UCC Client Upload Request Headers: {}", httpHeaders);
        log.info("NSE UCC Client Upload Request Body: {}", body);

        // Sending Request
        HttpEntity<String> requestEntity = new HttpEntity<>(objectMapper.writeValueAsString(body), httpHeaders);
        ResponseEntity<String> response = restTemplate.postForEntity(url, requestEntity, String.class);
        String responseString = response.getBody();
        log.info("NSE UCC Client Upload Response: {}", responseString);
        return null;
    }

    public String getPanStatus(String token, JsonNode payload) throws JsonProcessingException {
        String route = ClientRoutes.GET_PAN_STATUS;
        String url = clientConfig.getBaseUrl() + route;
        log.info("NSE UCC Get Pan Status URL: {}", url);

        // Fetching token from database, else fetching from api
        String authToken = StringUtils.isBlank(token) ? this.authenticate() : token;

        // Http Headers
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.setContentType(MediaType.APPLICATION_JSON);
        httpHeaders.add("Authorization", "Bearer " + authToken);

        // Request Payload
        Map<String, Object> body = new HashMap<>();
        body.put("request", payload);

        log.info("NSE UCC Get Pan Status Request Headers: {}", httpHeaders);
        log.info("NSE UCC Get Pan Status Request Body: {}", body);

        // Sending Request
        HttpEntity<String> requestEntity = new HttpEntity<>(objectMapper.writeValueAsString(body), httpHeaders);
        ResponseEntity<String> response = restTemplate.postForEntity(url, requestEntity, String.class);
        String responseString = response.getBody();
        log.info("NSE UCC Get Pan Status Response: {}", responseString);


        return "";
    }

    public String getUccPanStatus(String token, JsonNode payload) throws JsonProcessingException {
        String route = ClientRoutes.GET_UCC_PAN_STATUS;
        String url = clientConfig.getBaseUrl() + route;

        // Fetching token from database, else fetching from api
        String authToken = StringUtils.isBlank(token) ? this.authenticate() : token;

        // Http Headers
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.setContentType(MediaType.APPLICATION_JSON);
        httpHeaders.add("Authorization", "Bearer " + authToken);

        // Request Payload
        Map<String, Object> body = new HashMap<>();
        body.put("request", payload);

        log.info("NSE UCC Get Ucc Pan Status Request Headers: {}", httpHeaders);
        log.info("NSE UCC Get Ucc Pan Status Request Body: {}", body);

        // Sending Request
        HttpEntity<String> requestEntity = new HttpEntity<>(objectMapper.writeValueAsString(body), httpHeaders);
        ResponseEntity<String> response = restTemplate.postForEntity(url, requestEntity, String.class);
        String responseString = response.getBody();
        log.info("NSE UCC Get Ucc Pan Status Response: {}", responseString);

        return "";
    }

    @Data
    public static class ClientConfig {

        @NotBlank(message = "Base URL is required")
        private String baseUrl = "https://www.nseindia.com";

        @NotBlank(message = "Username is required")
        private String username = "username";

        @NotBlank(message = "Password is required")
        private String password = "password";

        @NotBlank(message = "API Name is required")
        private String apiName = "apiName";

        public ClientConfig(String baseUrl, String username, String password, String apiName) {
            this.baseUrl = baseUrl;
            this.username = username;
            this.password = password;
            this.apiName = apiName;
        }

        public ClientConfig(OneclickProperties.NseUccConfig config) {
            this.baseUrl = config.baseUrl();
            this.username = config.username();
            this.password = config.password();
            this.apiName = config.apiName();
        }

        public ClientConfig() {
        }
    }

    public static class ClientRoutes {
        public static final String AUTHENTICATE = "/uciservice/api/auth";
        public static final String CLIENT_UPLOAD = "/uciservice/api/clientupload";
        public static final String GET_PAN_STATUS = "/uciservice/api/getpanstatus";
        public static final String GET_UCC_PAN_STATUS = "/uciservice/api/getuccpanstatus";
    }
}
