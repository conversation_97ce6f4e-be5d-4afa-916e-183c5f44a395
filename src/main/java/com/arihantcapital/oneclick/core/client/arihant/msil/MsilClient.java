package com.arihantcapital.oneclick.core.client.arihant.msil;

import com.arihantcapital.oneclick.OneclickProperties;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Service(value = "msilClient")
public class MsilClient {

    private final RestTemplate restTemplate = new RestTemplate();
    private final ObjectMapper objectMapper = new ObjectMapper();
    private MsilClient.ClientConfig clientConfig = new ClientConfig();

    public MsilClient(MsilClient.ClientConfig clientConfig) {
        this.clientConfig = clientConfig;
        log.info("MSIL Client Config: {}", clientConfig);
    }

    public MsilClient() {
    }

    public String insertClientDetails(List<JsonNode> users) throws JsonProcessingException {
        String route = ClientRoutes.INSERT_CLIENT_DETAILS;
        String url = clientConfig.getBaseUrl() + route;

        // Http Headers
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.setContentType(MediaType.APPLICATION_JSON);
        httpHeaders.add("Authorization", "Basic " + clientConfig.getToken());

        // Request Body
        Map<String, Object> body = new HashMap<>();
        Map<String, Object> request = new HashMap<>();
        Map<String, List<JsonNode>> data = new HashMap<>();
        data.put("users", users);
        request.put("data", data);
        body.put("request", request);

        log.info("MSIL Insert Client Details Bulk Request Headers: {}", httpHeaders);
        log.info("MSIL Insert Client Details Bulk Request Body: {}", body);

        // Sending Request
        HttpEntity<String> requestEntity = new HttpEntity<>(objectMapper.writeValueAsString(body), httpHeaders);
        ResponseEntity<String> response = restTemplate.postForEntity(url, requestEntity, String.class);
        String responseString = response.getBody();
        log.info("MSIL Insert Client Details bulk Response: {}", responseString);

        return "";
    }

    public String insertClientDetails(JsonNode user) throws JsonProcessingException {
        String route = ClientRoutes.INSERT_CLIENT_DETAILS;
        String url = clientConfig.getBaseUrl() + route;

        // Http Headers
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.setContentType(MediaType.APPLICATION_JSON);
        httpHeaders.add("Authorization", "Basic " + clientConfig.getToken());

        // Request Body
        Map<String, Object> body = new HashMap<>();
        Map<String, Object> request = new HashMap<>();
        Map<String, List<JsonNode>> data = new HashMap<>();
        data.put("users", List.of(user));
        request.put("data", data);
        body.put("request", request);

        log.info("MSIL Insert Client Details Request Headers: {}", httpHeaders);
        log.info("MSIL Insert Client Details Request Body: {}", body);

        // Sending Request
        HttpEntity<String> requestEntity = new HttpEntity<>(objectMapper.writeValueAsString(body), httpHeaders);
        ResponseEntity<String> response = restTemplate.postForEntity(url, requestEntity, String.class);
        String responseString = response.getBody();
        log.info("MSIL Insert Client Details Response: {}", responseString);

        return "";
    }

    public String updateClientDetails(List<JsonNode> users) throws JsonProcessingException {
        String route = ClientRoutes.UPDATE_CLIENT_DETAILS;
        String url = clientConfig.getBaseUrl() + route;

        // Http Headers
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.setContentType(MediaType.APPLICATION_JSON);
        httpHeaders.add("Authorization", "Basic " + clientConfig.getToken());

        // Request Body
        Map<String, Object> body = new HashMap<>();
        Map<String, Object> request = new HashMap<>();
        Map<String, List<JsonNode>> data = new HashMap<>();
        data.put("users", users);
        request.put("data", data);
        body.put("request", request);

        log.info("MSIL Update Client Details bulk Request Headers: {}", httpHeaders);
        log.info("MSIL Update Client Details Bulk Request Body: {}", body);

        // Sending Request
        HttpEntity<String> requestEntity = new HttpEntity<>(objectMapper.writeValueAsString(body), httpHeaders);
        ResponseEntity<String> response = restTemplate.postForEntity(url, requestEntity, String.class);
        String responseString = response.getBody();
        log.info("MSIL Update Client Details Bulk Response: {}", responseString);

        return "";
    }

    public String updateClientDetails(JsonNode user) throws JsonProcessingException {
        String route = ClientRoutes.UPDATE_CLIENT_DETAILS;
        String url = clientConfig.getBaseUrl() + route;
        // Http Headers
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.setContentType(MediaType.APPLICATION_JSON);
        httpHeaders.add("Authorization", "Basic " + clientConfig.getToken());

        // Request Body
        Map<String, Object> body = new HashMap<>();
        Map<String, Object> request = new HashMap<>();
        Map<String, List<JsonNode>> data = new HashMap<>();
        data.put("users", List.of(user));
        request.put("data", data);
        body.put("request", request);

        log.info("MSIL Update Client Details Request Headers: {}", httpHeaders);
        log.info("MSIL Update Client Details Request Body: {}", body);

        // Sending Request
        HttpEntity<String> requestEntity = new HttpEntity<>(objectMapper.writeValueAsString(body), httpHeaders);
        ResponseEntity<String> response = restTemplate.postForEntity(url, requestEntity, String.class);
        String responseString = response.getBody();
        log.info("MSIL Update Client Details Response: {}", responseString);

        return "";
    }

    @Data
    public static class ClientConfig {
        @NotBlank(message = "Base URL is required")
        private String baseUrl = "https://cug-ws.arihantcapital.com";

        @NotBlank(message = "Token is required")
        private String token = "YWNtbDo0MzIxQEFyaWhhbnQ=";


        public ClientConfig(String baseUrl, String token) {
            this.baseUrl = baseUrl;
            this.token = token;
        }

        public ClientConfig(OneclickProperties.MsilConfig config) {
            this.baseUrl = config.baseUrl();
            this.token = config.token();
        }

        public ClientConfig() {
        }
    }


    // Client Routes
    public static class ClientRoutes {
        public static final String INSERT_CLIENT_DETAILS = "/clientdetails-service/InsertClientDetails/1.0.0";
        public static final String UPDATE_CLIENT_DETAILS = "/clientdetails-service/UpdateClientDetails/1.0.0";
    }
}
