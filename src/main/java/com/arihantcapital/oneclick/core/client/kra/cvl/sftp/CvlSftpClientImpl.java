package com.arihantcapital.oneclick.core.client.kra.cvl.sftp;


import com.arihantcapital.oneclick.OneclickProperties;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import org.apache.sshd.client.SshClient;
import org.apache.sshd.client.session.ClientSession;
import org.apache.sshd.sftp.client.SftpClient;
import org.apache.sshd.sftp.client.SftpClientFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.time.Duration;

@Service(value = "cvlSftpClient")
public class CvlSftpClientImpl {
    private static final Logger log = LoggerFactory.getLogger(CvlSftpClientImpl.class);


    private CvlSftpClientImpl.ClientConfig clientConfig = new ClientConfig();

    private SshClient sshClient;
    private ClientSession session;
    private SftpClient sftpClient;

    private static final Duration DEFAULT_TIMEOUT = Duration.ofSeconds(30);
    private static final Duration DEFAULT_AUTH_TIMEOUT = Duration.ofSeconds(10);

    public CvlSftpClientImpl(CvlSftpClientImpl.ClientConfig clientConfig) {
        this.clientConfig = clientConfig;
        log.info("Cvl Sftp Client Config: {}", clientConfig);
    }

    public CvlSftpClientImpl() {
    }

    /**
     * Connect using username and password
     */
    public void connect() throws IOException {
        String host = clientConfig.getHost();
        int port = Integer.parseInt(clientConfig.getPort());
        String username = clientConfig.getUsername();
        String password = clientConfig.getPassword();

        //Initialze SSH Client
        sshClient = SshClient.setUpDefaultClient();
        sshClient.start();

        //Create Session
        session = sshClient.connect(username, host, port).verify(DEFAULT_TIMEOUT).getSession();
        session.addPasswordIdentity(password);

        //Authentication
        boolean authenticated = session.auth().verify(DEFAULT_AUTH_TIMEOUT).isSuccess();
        if (authenticated) {
            log.info("Successfully Authenticated to CVl KRA SFTP Server using username and password");
        }

        //Create SFTP Client
        SftpClientFactory factory = SftpClientFactory.instance();

        sftpClient = factory.createSftpClient(session);
        log.info("Sftp conncetion established successfully to {}:{}", host, port);
    }

    @Data
    public static class ClientConfig {
        @NotBlank(message = "Host is required")
        private String host = "**************";

        @NotBlank(message = "Port is required")
        private String port = "22";

        @NotBlank(message = "Username is required")
        private String username = "arihant";

        @NotBlank(message = "Password is required")
        private String password = "arihant@123";

        public ClientConfig(String host, String port, String username, String password) {
            this.host = host;
            this.port = port;
            this.username = username;
            this.password = password;
        }

        public ClientConfig(OneclickProperties.KraConfig.CvlConfig.SftpConfig config) {
            this.host = config.host();
            this.port = config.port();
            this.username = config.username();
            this.password = config.password();
        }

        public ClientConfig() {
        }
    }

}
