package com.arihantcapital.oneclick.core.client.bse.starmutualfund;


import com.arihantcapital.oneclick.OneclickProperties;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.HashMap;
import java.util.Map;

@Slf4j
@Service(value = "bseStarMutualFundClient")
public class BseStarMutualFundClientImpl {

    private final RestTemplate restTemplate = new RestTemplate();
    private final ObjectMapper objectMapper = new ObjectMapper();
    private BseStarMutualFundClientImpl.ClientConfig clientConfig = new ClientConfig();

    public BseStarMutualFundClientImpl(BseStarMutualFundClientImpl.ClientConfig clientConfig) {
        this.clientConfig = clientConfig;
        log.info("BSE Star Mutual Fund Client Config: {}", clientConfig);
    }

    public BseStarMutualFundClientImpl() {
    }

    public String registration(String regnType, String param) throws JsonProcessingException {
        String url = clientConfig.getBaseUrl() + ClientRoutes.REGISTRATION;
        log.info("Initiating BSE Star Mutual Fund registration (RestTemplate) for regnType: {}, URl: {}", regnType, url);

        // Http Headers
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.setContentType(MediaType.APPLICATION_JSON);

        // Request Body
        Map<String, String> body = new HashMap<>();
        body.put("MemberCode", clientConfig.getMemberCode());
        body.put("UserId", clientConfig.getUsername());
        body.put("Password", clientConfig.getPassword());
        body.put("RegnType", regnType);
        body.put("Param", param);
        body.put("Filler1", "");
        body.put("Filler2", "");

        log.info("BSE Star Mutual Fund Registration Request Headers: {}", httpHeaders);
        log.info("BSE Star Mutual Fund Registration Request Body: {}", body);

        // Sending Request
        HttpEntity<String> requestEntity = new HttpEntity<>(objectMapper.writeValueAsString(body), httpHeaders);
        ResponseEntity<String> response = restTemplate.postForEntity(url, requestEntity, String.class);
        String responseString = response.getBody();
        log.info("BSE Star Mutual Fund Registration Response: {}", responseString);


        return "";
    }

    public String registrationV183(String regnType, String param) throws JsonProcessingException {
        String url = clientConfig.getBaseUrl() + ClientRoutes.REGISTRATION;
        log.info("Initiating BSE Star Mutual Fund registration V183 (RestTemplate) for regnType: {}, URl: {}", regnType, url);

        // Http Headers
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.setContentType(MediaType.APPLICATION_JSON);

        // Request Body
        Map<String, String> body = new HashMap<>();
        body.put("MemberCode", clientConfig.getMemberCode());
        body.put("UserId", clientConfig.getUsername());
        body.put("Password", clientConfig.getPassword());
        body.put("RegnType", regnType);
        body.put("Param", param);
        body.put("Filler1", "");
        body.put("Filler2", "");

        log.info("BSE Star Mutual Fund Registration V183 Request Headers: {}", httpHeaders);
        log.info("BSE Star Mutual Fund Registration V183 Request Body: {}", body);

        // Sending Request
        HttpEntity<String> requestEntity = new HttpEntity<>(objectMapper.writeValueAsString(body), httpHeaders);
        ResponseEntity<String> response = restTemplate.postForEntity(url, requestEntity, String.class);
        String responseString = response.getBody();
        log.info("BSE Star Mutual Fund Registration V183 Response: {}", responseString);


        return "";
    }

    public String nomineeRegistrationV56(String regnType, String param) throws JsonProcessingException {
        String url = clientConfig.getBaseUrl() + ClientRoutes.REGISTRATION;
        log.info("Initiating BSE Star Mutual Fund nominee registration V56 (RestTemplate) for regnType: {}, URl: {}", regnType, url);

        // Http Headers
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.setContentType(MediaType.APPLICATION_JSON);

        // Request Body
        Map<String, String> body = new HashMap<>();
        body.put("MemberCode", clientConfig.getMemberCode());
        body.put("UserId", clientConfig.getUsername());
        body.put("Password", clientConfig.getPassword());
        body.put("RegnType", regnType);
        body.put("Type", "NOMINEE");
        body.put("Param", param);
        body.put("FILLER1", "");
        body.put("FILLER2", "");
        body.put("FILLER3", "");

        log.info("BSE Star Mutual Fund nominee registration V56 Request Headers: {}", httpHeaders);
        log.info("BSE Star Mutual Fund nominee registration V56 Request Body: {}", body);

        // Sending Request
        HttpEntity<String> requestEntity = new HttpEntity<>(objectMapper.writeValueAsString(body), httpHeaders);
        ResponseEntity<String> response = restTemplate.postForEntity(url, requestEntity, String.class);
        String responseString = response.getBody();
        log.info("BSE Star Mutual Fund nominee registration V56 Response: {}", responseString);


        return "";
    }

    @Data
    public static class ClientConfig {

        @NotBlank(message = "Base URL is required")
        private String baseUrl = "https://www.bsestarmf.in";

        @NotBlank(message = "Member Code is required")
        private String memberCode = "0313";

        @NotBlank(message = "Username is required")
        private String username = "031306";

        @NotBlank(message = "Password is required")
        private String password = "Arihant@280525";


        public ClientConfig(String baseUrl, String memberCode, String username, String password) {
            this.baseUrl = baseUrl;
            this.memberCode = memberCode;
            this.username = username;
            this.password = password;
        }

        public ClientConfig(OneclickProperties.BseStarMutualFundConfig config) {
            this.baseUrl = config.baseUrl();
            this.memberCode = config.memberCode();
            this.username = config.username();
            this.password = config.password();
        }

        public ClientConfig() {
        }
    }

    //Client Routes
    public static class ClientRoutes {
        public static final String REGISTRATION = "/BSEMFWEBAPI/UCCAPI/UCCRegistration";
        public static final String REGISTRATION_V183 = "/BSEMFWEBAPI/UCCAPI/UCCRegistrationV183";
        public static final String NOMINEE_REGISTRATION_V56 = "/BSEMFWEBAPI/api/mfupload/RegistationV56/w";
    }
}
