package com.arihantcapital.oneclick.core.client.bse.ucc;


import com.arihantcapital.oneclick.OneclickProperties;
import com.fasterxml.jackson.databind.JsonNode;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.w3c.dom.NodeList;

import javax.crypto.Cipher;
import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import java.io.ByteArrayInputStream;
import java.math.BigInteger;
import java.security.KeyFactory;
import java.security.MessageDigest;
import java.security.PublicKey;
import java.security.interfaces.RSAPublicKey;
import java.security.spec.RSAPublicKeySpec;
import java.security.spec.X509EncodedKeySpec;
import java.util.Base64;

@Slf4j
@Service(value = "bseUccClient")
public class BseUccClientImpl {

    private final RestTemplate restTemplate = new RestTemplate();
    private BseUccClientImpl.ClientConfig clientConfig = new BseUccClientImpl.ClientConfig();

    public BseUccClientImpl(BseUccClientImpl.ClientConfig clientConfig) {
        this.clientConfig = clientConfig;
        log.info("BSE UCC Client Config: {}", clientConfig);
    }

    public BseUccClientImpl() {
    }

    public String addUccData(JsonNode payload) throws Exception {
        String route = ClientRoutes.RESET_PASSWORD;
        String url = clientConfig.getBaseUrl() + route;

        // Hashing the payload
        String hashedPayload = this.SHA256Encode(payload.toString());
        log.info("BSE UCC Add Ucc Data Hashed Payload: {}", hashedPayload);

        // Converting public key xml to pem format
        String pemPublicKey = parsePublicKeyStringXML(clientConfig.getPublicKeyXml());
        log.info("BSE UCC Add Ucc Data Public Key: {}", pemPublicKey);

        // Encrypting the payload
        String encryptedPayload = RSAEncrypt(hashedPayload, pemPublicKey);
        log.info("BSE UCC Add Ucc Data Encrypted Payload: {}", encryptedPayload);

        // Encode password with MD5
        String encodedPassword = MD5Encode(clientConfig.getPassword());
        log.info("BSE UCC Add Ucc Data Encoded Password: {}", encodedPassword);

        // Http Headers
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.setContentType(MediaType.APPLICATION_JSON);
        httpHeaders.add("MembeCode", clientConfig.getMemberCode());
        httpHeaders.add("UserID", clientConfig.getUsername());
        httpHeaders.add("Password", encodedPassword);
        httpHeaders.add("Checksum", encryptedPayload);

        // Request Payload
        String body = payload.toString();
        log.info("BSE UCC Add Ucc Data Request Headers: {}", httpHeaders);
        log.info("BSE UCC Add Ucc Data Request Body: {}", body);

        // Sending Request
        HttpEntity<String> requestEntity = new HttpEntity<>(body, httpHeaders);
        ResponseEntity<String> response = restTemplate.postForEntity(url, requestEntity, String.class);
        String responseString = response.getBody();
        log.info("BSE UCC Add Ucc Data Response: {}", responseString);

        return "";
    }

    public String resetPassword(JsonNode payload) throws Exception {
        String route = ClientRoutes.RESET_PASSWORD;
        String url = clientConfig.getBaseUrl() + route;

        // Hashing the payload
        String hashedPayload = this.SHA256Encode(payload.toString());
        log.info("BSE UCC Reset Password Hashed Payload: {}", hashedPayload);

        // Converting public key xml to pem format
        String pemPublicKey = parsePublicKeyStringXML(clientConfig.getPublicKeyXml());
        log.info("BSE UCC Reset Password Public Key: {}", pemPublicKey);

        // Encrypting the payload
        String encryptedPayload = RSAEncrypt(hashedPayload, pemPublicKey);
        log.info("BSE UCC Reset Password Encrypted Payload: {}", encryptedPayload);

        // Encode password with MD5
        String encodedPassword = MD5Encode(clientConfig.getPassword());
        log.info("BSE UCC Reset Password Encoded Password: {}", encodedPassword);

        // Http Headers
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.setContentType(MediaType.APPLICATION_JSON);
        httpHeaders.add("MembeCode", clientConfig.getMemberCode());
        httpHeaders.add("UserID", clientConfig.getUsername());
        httpHeaders.add("Password", encodedPassword);
        httpHeaders.add("Checksum", encryptedPayload);

        // Request Payload
        String body = payload.toString();
        log.info("BSE UCC Reset Password Request Headers: {}", httpHeaders);
        log.info("BSE UCC Reset Password Request Body: {}", body);

        // Sending Request
        HttpEntity<String> requestEntity = new HttpEntity<>(body, httpHeaders);
        ResponseEntity<String> response = restTemplate.postForEntity(url, requestEntity, String.class);
        String responseString = response.getBody();
        log.info("BSE UCC Reset Password Response: {}", responseString);

        return "";
    }

    public String getPanStatus(JsonNode payload) throws Exception {
        String route = ClientRoutes.GET_PAN_STATUS;
        String url = clientConfig.getBaseUrl() + route;

        // Hashing the payload
        String hashedPayload = this.SHA256Encode(payload.toString());
        log.info("BSE UCC Get Pan Status Hashed Payload: {}", hashedPayload);

        // Converting public key xml to pem format
        String pemPublicKey = parsePublicKeyStringXML(clientConfig.getPublicKeyXml());
        log.info("BSE UCC Get Pan Status Public Key: {}", pemPublicKey);

        // Encrypting the payload
        String encryptedPayload = RSAEncrypt(hashedPayload, pemPublicKey);
        log.info("BSE UCC Get Pan Status Encrypted Payload: {}", encryptedPayload);

        // Encode password with MD5
        String encodedPassword = MD5Encode(clientConfig.getPassword());
        log.info("BSE UCC Get Pan Status Encoded Password: {}", encodedPassword);

        // Http Headers
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.setContentType(MediaType.APPLICATION_JSON);
        httpHeaders.add("MembeCode", clientConfig.getMemberCode());
        httpHeaders.add("UserID", clientConfig.getUsername());
        httpHeaders.add("Password", encodedPassword);
        httpHeaders.add("Checksum", encryptedPayload);

        // Request Payload
        String body = payload.toString();
        log.info("BSE UCC Get Pan Status Request Headers: {}", httpHeaders);
        log.info("BSE UCC Get Pan Status Request Body: {}", body);

        // Sending Request
        HttpEntity<String> requestEntity = new HttpEntity<>(body, httpHeaders);
        ResponseEntity<String> response = restTemplate.postForEntity(url, requestEntity, String.class);
        String responseString = response.getBody();
        log.info("BSE UCC Get Pan Status Response: {}", responseString);
        return "";
    }

    public String addCustodianData() {
        return "";
    }

    @Data
    public static class ClientConfig {

        @NotBlank(message = "API Version is required")
        private String apiVersion = "V5";

        @NotBlank(message = "Base URL is required")
        private String baseUrl = "https://ucc.bseindia.com";

        @NotBlank(message = "Member Code is required")
        private String memberCode = "0313";

        @NotBlank(message = "Username is required")
        private String username = "0313.0313D";

        @NotBlank(message = "Password is required")
        private String password = "Bse@54321";

        @NotBlank(message = "Public Key XML is required")
        private String publicKeyXml = "<RSAKeyValue><Modulus>xWlQk3gh0ImHOXhnhtbWZZ3FqtHI+v+5CL5Hs8mR/nS/Q/ewNPMWJ6n0RY6rQysH0NP3AoAujI3P2p7VC9B2e7QyVcDucLw5GiqWJuxaJ76OoeHL5shzglFZrWkDLrccHGNUaH04co8G3RNM3lu04XU2CMQt0awuUsDvkCkJyeVV+ouyVTDF0mP7a1bGSQj9tRnl0cNh4PElpAd4vAnn+TAgMJBJQ/hLqcx+IUH0XZfVBpfp0THOIuw8E83FQvukJWKFrpAxoTXczSQE7Zqsmjg3GAQ0UMsoikDbGL1Vhe7CHmuj9QVGbUwN3NBhDcGpe3JmNT5QWWzsqYMZD9O08w==</Modulus><Exponent>AQAB</Exponent></RSAKeyValue>";

        public ClientConfig(String apiVersion, String baseUrl, String memberCode, String username, String password, String publicKeyXml) {
            this.apiVersion = apiVersion;
            this.baseUrl = baseUrl;
            this.memberCode = memberCode;
            this.username = username;
            this.password = password;
            this.publicKeyXml = publicKeyXml;
        }

        public ClientConfig(OneclickProperties.BseUccConfig config) {
            this.apiVersion = config.apiVersion();
            this.baseUrl = config.baseUrl();
            this.memberCode = config.memberCode();
            this.username = config.username();
            this.password = config.password();
            this.publicKeyXml = config.publicKeyXml();
        }

        public ClientConfig() {
        }

    }


    // Service routes
    public static class ClientRoutes {
        public static final String ADD_UCC_DATA = "/UCC_REST_API_SERVICE/UCCService.svc/AddUCCData_V5";
        public static final String RESET_PASSWORD = "/UCC_REST_API_SERVICE/UCCService.svc/ResetPassword";
        public static final String GET_PAN_STATUS = "/UCC_REST_API_SERVICE/UCCService.svc/PANStatus";
        public static final String ADD_CUSTODIAN_DATA = "/UCC_REST_API_SERVICE/UCCService.svc/AddCustodianData";
    }

    /**
     * @param data The data to be encoded.
     * @return The SHA256 encoded value.
     * @private This method is for internal use.
     * <p>
     * Returns the SHA256 encoded value of the given data.
     */
    private String SHA256Encode(String data) {
        try {
            MessageDigest digest = MessageDigest.getInstance("SHA-256");
            byte[] hash = digest.digest(data.getBytes("UTF-8"));
            return bytesToHex(hash);
        } catch (Exception e) {
            throw new RuntimeException("Error encoding SHA256", e);
        }
    }

    /**
     * @param publicKeyStringXML The XML string representing the public key.
     * @return The public key in PEM format.
     * @throws Exception if parsing fails
     * @private This method is for internal use.
     * <p>
     * Parses a public key in XML format and returns it in PEM format.
     */
    private String parsePublicKeyStringXML(String publicKeyStringXML) throws Exception {
        try {
            DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
            DocumentBuilder builder = factory.newDocumentBuilder();
            Document doc = builder.parse(new ByteArrayInputStream(publicKeyStringXML.getBytes()));

            Element root = doc.getDocumentElement();
            NodeList modulusNodes = root.getElementsByTagName("Modulus");
            NodeList exponentNodes = root.getElementsByTagName("Exponent");

            if (modulusNodes.getLength() == 0 || exponentNodes.getLength() == 0) {
                throw new Exception("XML::PARSE_FAILED::PUBLIC_KEY");
            }

            String modulusBase64 = modulusNodes.item(0).getTextContent();
            String exponentBase64 = exponentNodes.item(0).getTextContent();

            byte[] modulusBytes = Base64.getDecoder().decode(modulusBase64);
            byte[] exponentBytes = Base64.getDecoder().decode(exponentBase64);

            BigInteger modulus = new BigInteger(1, modulusBytes);
            BigInteger exponent = new BigInteger(1, exponentBytes);

            // Create RSA public key
            KeyFactory keyFactory = KeyFactory.getInstance("RSA");
            RSAPublicKeySpec keySpec = new RSAPublicKeySpec(modulus, exponent);
            RSAPublicKey publicKey = (RSAPublicKey) keyFactory.generatePublic(keySpec);

            // Convert to PEM format
            return convertToPEM(publicKey);

        } catch (Exception e) {
            throw new Exception("XML::PARSE_FAILED::PUBLIC_KEY");
        }
    }

    /**
     * @param data            The data to be encrypted.
     * @param publicKeyString The RSA public key in PEM format.
     * @return The encrypted data in base64 format.
     * @private This method is for internal use.
     * <p>
     * Encrypts the given data using the RSA public key.
     */
    private String RSAEncrypt(String data, String publicKeyString) {
        try {
            // Parse PEM format public key
            PublicKey publicKey = parsePublicKeyFromPEM(publicKeyString);

            // Encrypt using RSA with PKCS1 padding
            Cipher cipher = Cipher.getInstance("RSA/ECB/PKCS1Padding");
            cipher.init(Cipher.ENCRYPT_MODE, publicKey);

            byte[] encryptedBytes = cipher.doFinal(data.getBytes("UTF-8"));
            return Base64.getEncoder().encodeToString(encryptedBytes);

        } catch (Exception e) {
            throw new RuntimeException("Error encrypting data", e);
        }
    }

    /**
     * @param data The data to be encoded.
     * @return The MD5 hash of the data.
     * @private This method is for internal use.
     * <p>
     * Generates an MD5 hash for the given data.
     */
    private String MD5Encode(String data) {
        try {
            MessageDigest digest = MessageDigest.getInstance("MD5");
            byte[] hash = digest.digest(data.getBytes("UTF-8"));
            return bytesToHex(hash);
        } catch (Exception e) {
            throw new RuntimeException("Error encoding MD5", e);
        }
    }

    // Helper method to convert bytes to hex string
    private String bytesToHex(byte[] bytes) {
        StringBuilder result = new StringBuilder();
        for (byte b : bytes) {
            result.append(String.format("%02x", b));
        }
        return result.toString();
    }

    // Helper method to convert RSA public key to PEM format
    private String convertToPEM(RSAPublicKey publicKey) {
        try {
            byte[] encoded = publicKey.getEncoded();
            String base64 = Base64.getEncoder().encodeToString(encoded);

            StringBuilder pem = new StringBuilder();
            pem.append("-----BEGIN PUBLIC KEY-----\n");

            // Split base64 string into 64-character lines
            for (int i = 0; i < base64.length(); i += 64) {
                int end = Math.min(i + 64, base64.length());
                pem.append(base64.substring(i, end)).append("\n");
            }

            pem.append("-----END PUBLIC KEY-----");
            return pem.toString();

        } catch (Exception e) {
            throw new RuntimeException("Error converting to PEM format", e);
        }
    }

    // Helper method to parse public key from PEM format
    private PublicKey parsePublicKeyFromPEM(String pemKey) {
        try {
            String publicKeyPEM = pemKey.replace("-----BEGIN PUBLIC KEY-----", "").replace("-----END PUBLIC KEY-----", "").replace("-----BEGIN RSA PUBLIC KEY-----", "").replace("-----END RSA PUBLIC KEY-----", "").replaceAll("\\s", "");

            byte[] decoded = Base64.getDecoder().decode(publicKeyPEM);
            X509EncodedKeySpec keySpec = new X509EncodedKeySpec(decoded);
            KeyFactory keyFactory = KeyFactory.getInstance("RSA");

            return keyFactory.generatePublic(keySpec);

        } catch (Exception e) {
            throw new RuntimeException("Error parsing PEM public key", e);
        }
    }
}
