package com.arihantcapital.oneclick.core.client.kra.cvl.pancheck.json;

import com.arihantcapital.oneclick.OneclickProperties;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.dataformat.xml.XmlMapper;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.SecureRandom;
import java.util.Base64;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Service(value = "CvlPancheckJsonClient")
public class CvlPancheckJsonClientImpl {

    //Instance variables
    private final String version = "";
    private final RestTemplate restTemplate = new RestTemplate();
    private final ObjectMapper objectMapper = new ObjectMapper();
    private final XmlMapper xmlMapper = new XmlMapper();

    private CvlPancheckJsonClientImpl.ClientConfig clientConfig = new ClientConfig();

    private String token = null;
    private String tokenExpiry = null;

    public CvlPancheckJsonClientImpl(CvlPancheckJsonClientImpl.ClientConfig clientConfig) {
        this.clientConfig = clientConfig;
        log.info("Cvl Pancheck Json Client Config: {}", clientConfig);
    }

    public CvlPancheckJsonClientImpl() {
    }

    public JsonNode getToken(String tokenValidTime) throws Exception {
        String route = ClientRoutes.GET_TOKEN;

        // Request Payload
        Map<String, String> payload = new HashMap<>();
        payload.put("username", clientConfig.getUsername());
        payload.put("poscode", clientConfig.getPosCode());
        payload.put("password", clientConfig.getPassword());

        // Http Headers
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.setContentType(MediaType.APPLICATION_JSON);
        httpHeaders.setAccept(List.of(MediaType.ALL));
        httpHeaders.add("user-agent", clientConfig.getUserAgent());
        httpHeaders.add("api_key", clientConfig.getApiKey());
        httpHeaders.add("tokenvalidtime", StringUtils.isBlank(tokenValidTime) ? clientConfig.getTokenValidTime() : tokenValidTime);


        log.info("Get Token Request Headers: {}", httpHeaders);
        log.info("Get Token Request Body: {}", payload);

        // Payload Encryption
        byte[] iv = generateIV();
        String ivBase64 = Base64.getEncoder().encodeToString(iv);
        String data = objectMapper.writeValueAsString(payload);
        String encryptedData = encryptString(clientConfig.getAesKey(), data, iv);
        String encryptedRequestData = ivBase64 + ":" + encryptedData;

        String finalPayload = String.format("\"%s\"", encryptedRequestData);

        log.info("Get Token Encrypted Request Body: {}", finalPayload);

        // Sending Request
        HttpEntity<String> requestEntity = new HttpEntity<String>(finalPayload, httpHeaders);
        ResponseEntity<String> response = restTemplate.postForEntity(clientConfig.getBaseUrl() + route, requestEntity, String.class);
        String responseString = response.getBody();
        log.info("Get Token Encrypted Response: {}", responseString);

        // Validate response
        if (responseString == null || responseString.trim().isEmpty()) {
            throw new Exception("Empty response received from server");
        }

        // Decrypt Response
        String cleanedResponse = responseString.replaceAll("^\"|\"$", "");
        String[] splittedStrings = cleanedResponse.split(":");
        if (splittedStrings.length != 2) {
            throw new Exception("Invalid response format. Expected 'iv:encryptedData' but got: " + responseString);
        }

        String responseIv = splittedStrings[0];
        String encryptedText = splittedStrings[1];
        String decryptedResponseData = decryptString(clientConfig.getAesKey(), encryptedText, responseIv);
        log.info("Get Token Decrypted String Response: {}", decryptedResponseData);

        if (decryptedResponseData == null) {
            throw new Exception("Failed to decrypt response data");
        }

        JsonNode responseJson = objectMapper.readTree(decryptedResponseData);
        log.info("Get Token Decrypted Json Response: {}", responseJson);

        return responseJson;
    }

    public JsonNode getPanStatus(String token, String panNumber) throws Exception {
        String route = ClientRoutes.GET_PAN_STATUS;

        // Request Payload
        Map<String, String> payload = new HashMap<>();
        payload.put("pan", panNumber);
        payload.put("poscode", clientConfig.getPosCode());


        // Http Headers
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.setContentType(MediaType.APPLICATION_JSON);
        httpHeaders.setAccept(List.of(MediaType.ALL));
        httpHeaders.add("user-agent", clientConfig.getUserAgent());
        httpHeaders.add("Token", token);


        log.info("Get Pan Status Request Headers: {}", httpHeaders);
        log.info("Get Pan Status Request Body: {}", payload);

        // Payload Encryption
        byte[] iv = generateIV();
        String ivBase64 = Base64.getEncoder().encodeToString(iv);
        String data = objectMapper.writeValueAsString(payload);
        String encryptedData = encryptString(clientConfig.getAesKey(), data, iv);
        String encryptedRequestData = ivBase64 + ":" + encryptedData;

        String finalPayload = String.format("\"%s\"", encryptedRequestData);

        log.info("Get Pan Status Encrypted Request Body: {}", finalPayload);

        // Sending Request
        HttpEntity<String> requestEntity = new HttpEntity<String>(finalPayload, httpHeaders);
        ResponseEntity<String> response = restTemplate.postForEntity(clientConfig.getBaseUrl() + route, requestEntity, String.class);
        String responseString = response.getBody();
        log.info("Get Pan Status Encrypted Response: {}", responseString);

        // Validate response
        if (responseString == null || responseString.trim().isEmpty()) {
            throw new Exception("Empty response received from server");
        }

        // Decrypt Response
        String cleanedResponse = responseString.replaceAll("^\"|\"$", "");
        String[] splittedStrings = cleanedResponse.split(":");
        if (splittedStrings.length != 2) {
            throw new Exception("Invalid response format. Expected 'iv:encryptedData' but got: " + responseString);
        }

        String responseIv = splittedStrings[0];
        String encryptedText = splittedStrings[1];
        String decryptedResponseData = decryptString(clientConfig.getAesKey(), encryptedText, responseIv);
        log.info("Get Pan Status Decrypted String Response: {}", decryptedResponseData);

        if (decryptedResponseData == null) {
            throw new Exception("Failed to decrypt response data");
        }

        JsonNode responseJson = objectMapper.readTree(decryptedResponseData);
        log.info("Get Pan Status Decrypted Json Response: {}", responseJson);
        JsonNode panStatus = objectMapper.readTree(responseJson.get("resdtls").asText());
        log.info("Get Pan Status Decrypted Json Response: {}", panStatus);

        return panStatus;
    }

    public JsonNode solicitPANDetailsFetchALLKRA(String token, String fetchType, String kraCode, String panNumber, String dob) throws Exception {
        String route = ClientRoutes.SOLICIT_PAN_DETAILS_FETCH_ALL_KRA;

        // Request Payload
        Map<String, Object> payload = new HashMap<>();

        Map<String, Object> appReqRoot = new HashMap<>();
        Map<String, Object> appPanInq = new HashMap<>();

        appPanInq.put("APP_PAN_NO", panNumber);
        appPanInq.put("APP_DOB_INCORP", dob);
        appPanInq.put("APP_POS_CODE", clientConfig.getPosCode());
        appPanInq.put("APP_RTA_CODE", clientConfig.getRtaCode());
        appPanInq.put("APP_KRA_CODE", kraCode);
        appPanInq.put("FETCH_TYPE", fetchType);
        appReqRoot.put("APP_PAN_INQ", appPanInq);
        payload.put("APP_REQ_ROOT", appReqRoot);


        // Http Headers
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.setContentType(MediaType.APPLICATION_JSON);
        httpHeaders.setAccept(List.of(MediaType.ALL));
        httpHeaders.add("user-agent", clientConfig.getUserAgent());
        httpHeaders.add("Token", token);


        log.info("SolicitPANDetailsFetchALLKRA Request Headers: {}", httpHeaders);
        log.info("SolicitPANDetailsFetchALLKRA Request Body: {}", payload);

        // Payload Encryption
        byte[] iv = generateIV();
        String ivBase64 = Base64.getEncoder().encodeToString(iv);
        String data = objectMapper.writeValueAsString(payload);
        String encryptedData = encryptString(clientConfig.getAesKey(), data, iv);
        String encryptedRequestData = ivBase64 + ":" + encryptedData;

        String finalPayload = String.format("\"%s\"", encryptedRequestData);

        log.info("SolicitPANDetailsFetchALLKRA Encrypted Request Body: {}", finalPayload);

        // Sending Request
        HttpEntity<String> requestEntity = new HttpEntity<String>(finalPayload, httpHeaders);
        ResponseEntity<String> response = restTemplate.postForEntity(clientConfig.getBaseUrl() + route, requestEntity, String.class);
        String responseString = response.getBody();
        log.info("SolicitPANDetailsFetchALLKRA Encrypted Response: {}", responseString);

        // Validate response
        if (responseString == null || responseString.trim().isEmpty()) {
            throw new Exception("Empty response received from server");
        }

        // Decrypt Response
        String cleanedResponse = responseString.replaceAll("^\"|\"$", "");
        String[] splittedStrings = cleanedResponse.split(":");
        if (splittedStrings.length != 2) {
            throw new Exception("Invalid response format. Expected 'iv:encryptedData' but got: " + responseString);
        }

        String responseIv = splittedStrings[0];
        String encryptedText = splittedStrings[1];
        String decryptedResponseData = decryptString(clientConfig.getAesKey(), encryptedText, responseIv);
        log.info("SolicitPANDetailsFetchALLKRA Decrypted String Response: {}", decryptedResponseData);

        if (decryptedResponseData == null) {
            throw new Exception("Failed to decrypt response data");
        }

        JsonNode responseJson = objectMapper.readTree(decryptedResponseData);
        log.info("SolicitPANDetailsFetchALLKRA Decrypted Json Response: {}", responseJson);
        JsonNode panStatus = objectMapper.readTree(responseJson.get("resdtls").asText());
        log.info("SolicitPANDetailsFetchALLKRA Decrypted Json Response: {}", panStatus);

        return panStatus;
    }

    public JsonNode insertUpdateKYCRecord(JsonNode jsonPayload) throws Exception {
        String route = ClientRoutes.INSERT_UPDATE_KYC_RECORD;
        return sendEncryptedRequest(route, jsonPayload, null, "InsertUpdateKYCRecord");
    }

    private JsonNode sendEncryptedRequest(String route, Object payload, String tokenValidTime, String operationName) throws Exception {
        // Http Headers
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.setContentType(MediaType.APPLICATION_JSON);
//        httpHeaders.add("Content-Type", "application/json; charset=utf-8");
        httpHeaders.setAccept(List.of(MediaType.ALL));
        httpHeaders.add("user-agent", clientConfig.getUserAgent());
        httpHeaders.add("api_key", clientConfig.getApiKey());

        if (tokenValidTime != null) {
            httpHeaders.add("tokenvalidtime", StringUtils.isBlank(tokenValidTime) ? clientConfig.getTokenValidTime() : tokenValidTime);
        }

        log.info("{} Request Headers: {}", operationName, httpHeaders);
        log.info("{} Request Body: {}", operationName, payload);

        // Payload Encryption
        byte[] iv = generateIV();
        String ivBase64 = Base64.getEncoder().encodeToString(iv);
        String data = objectMapper.writeValueAsString(payload);
        String encryptedData = encryptString(clientConfig.getAesKey(), data, iv);
        String encryptedRequestData = ivBase64 + ":" + encryptedData;

        String finalPayload = String.format("\"%s\"", encryptedRequestData);

        log.info("{} Encrypted Request Body: {}", operationName, finalPayload);

        // Sending Request
        HttpEntity<String> requestEntity = new HttpEntity<String>(finalPayload, httpHeaders);
        ResponseEntity<String> response = restTemplate.postForEntity(clientConfig.getBaseUrl() + route, requestEntity, String.class);
        String responseString = response.getBody();
        log.info("{} Encrypted Response: {}", operationName, responseString);

        // Validate response
        if (responseString == null || responseString.trim().isEmpty()) {
            throw new Exception("Empty response received from server");
        }

        // Decrypt Response
        String cleanedResponse = responseString.replaceAll("^\"|\"$", "");
        String[] splittedStrings = cleanedResponse.split(":");
        if (splittedStrings.length != 2) {
            throw new Exception("Invalid response format. Expected 'iv:encryptedData' but got: " + responseString);
        }

        String responseIv = splittedStrings[0];
        String encryptedText = splittedStrings[1];
        String decryptedResponseData = decryptString(clientConfig.getAesKey(), encryptedText, responseIv);
        log.info("{} Decrypted String Response: {}", operationName, decryptedResponseData);

        if (decryptedResponseData == null) {
            throw new Exception("Failed to decrypt response data");
        }

        JsonNode responseJson = objectMapper.readTree(decryptedResponseData);
        log.info("{} Decrypted Json Response: {}", operationName, responseJson);

        return responseJson;
    }


    // Service config
    @Data
    public static class ClientConfig {

        @NotBlank(message = "Base URL is required")
        private String baseUrl = "https://api.kracvl.com";

        @NotBlank(message = "User Agent is required")
        private String userAgent = "oneclick";

        @NotBlank(message = "API Key is required")
        private String apiKey = "1027d73fe55441ef95c96c0cd21601c0";

        @NotBlank(message = "AES Key is required")
        private String aesKey = "4c8c98585cfb425bb8ee3a003d535c8c";

        @NotBlank(message = "Username is required")
        private String username = "EKYC";

        @NotBlank(message = "Password is required")
        private String password = "Ekyc@1234567";

        @NotBlank(message = "POS Code is required")
        private String posCode = "1100043000";

        @NotBlank(message = "RTA Code is required")
        private String rtaCode = "1100043000";

        @NotBlank(message = "Token Valid Time is required")
        private String tokenValidTime = "3600";

        // Constructor for default configuration
        public ClientConfig(String baseUrl, String userAgent, String apiKey, String aesKey, String username, String password, String posCode, String rtaCode, String tokenValidTime) {
            this.baseUrl = baseUrl;
            this.userAgent = userAgent;
            this.apiKey = apiKey;
            this.aesKey = aesKey;
            this.username = username;
            this.password = password;
            this.posCode = posCode;
            this.rtaCode = rtaCode;
            this.tokenValidTime = tokenValidTime;
        }

        public ClientConfig(OneclickProperties.KraConfig.CvlConfig.PancheckConfig.JsonConfig config) {
            this.baseUrl = config.baseUrl();
            this.userAgent = config.userAgent();
            this.apiKey = config.apiKey();
            this.aesKey = config.aesKey();
            this.username = config.username();
            this.password = config.password();
            this.posCode = config.posCode();
            this.rtaCode = config.rtaCode();
            this.tokenValidTime = config.tokenValidTime();
        }

        public ClientConfig() {

        }
    }

    // Service routes
    public static class ClientRoutes {
        public static final String GET_TOKEN = "/int/api/GetToken";
        public static final String GET_PAN_STATUS = "/int/api/GetPanStatus";
        public static final String SOLICIT_PAN_DETAILS_FETCH_ALL_KRA = "/int/api/SolicitPANDetailsFetchALLKRA";
        public static final String INSERT_UPDATE_KYC_RECORD = "/int/api/InsertUpdateKYCRecord";
    }


    // Private utility methods
    private byte[] generateIV() {
        byte[] iv = new byte[16];
        new SecureRandom().nextBytes(iv);
        return iv;
    }

    private String encryptString(String aesKey, String data, byte[] iv) throws Exception {
        byte[] keyBytes = Base64.getDecoder().decode(aesKey);
        SecretKeySpec keySpec = new SecretKeySpec(keyBytes, "AES");
        IvParameterSpec ivSpec = new IvParameterSpec(iv);

        Cipher cipher = Cipher.getInstance("AES/CBC/PKCS5Padding");
        cipher.init(Cipher.ENCRYPT_MODE, keySpec, ivSpec);

        byte[] encryptedBytes = cipher.doFinal(data.getBytes(StandardCharsets.UTF_8));
        return Base64.getUrlEncoder().encodeToString(encryptedBytes);
    }

    private String decryptString(String aesKey, String encryptedText, String iv) {
        try {
            if (aesKey == null || encryptedText == null || iv == null) {
                log.error("Decryption failed: null parameters provided");
                return null;
            }

            byte[] keyBytes = Base64.getDecoder().decode(aesKey);
            byte[] ivBytes = Base64.getUrlDecoder().decode(iv);

            SecretKeySpec keySpec = new SecretKeySpec(keyBytes, "AES");
            IvParameterSpec ivSpec = new IvParameterSpec(ivBytes);

            Cipher cipher = Cipher.getInstance("AES/CBC/PKCS5Padding");
            cipher.init(Cipher.DECRYPT_MODE, keySpec, ivSpec);

            byte[] encryptedBytes = Base64.getUrlDecoder().decode(encryptedText);
            byte[] decryptedBytes = cipher.doFinal(encryptedBytes);
            return new String(decryptedBytes, StandardCharsets.UTF_8);
        } catch (Exception e) {
            log.error("Decryption error: {}", e.getMessage(), e);
            return null;
        }
    }


}

