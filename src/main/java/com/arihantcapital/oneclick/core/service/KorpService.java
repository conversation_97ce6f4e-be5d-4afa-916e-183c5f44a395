package com.arihantcapital.oneclick.core.service;

import com.arihantcapital.oneclick.OneclickProperties;
import com.arihantcapital.oneclick.core.client.korp.KorpClientImpl;
import com.fasterxml.jackson.databind.JsonNode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service(value = "korpService")
public class KorpService {
    private final OneclickProperties.KorpConfig config;
    private KorpClientImpl korpClient;

    public KorpService(OneclickProperties.KorpConfig config) {
        this.config = config;
        KorpClientImpl.ClientConfig clientConfig = new KorpClientImpl.ClientConfig(config);
        this.korpClient = new KorpClientImpl(clientConfig);
        log.info("KORP Service initialized with client config: {}", clientConfig);
    }

    public String authenticate() {
        try {
            return korpClient.authenticate();
        } catch (Exception e) {
            log.error(e.getMessage());
            return null;
        }
    }

    public JsonNode getClientMaster(String clientCode) {
        try {
            return korpClient.getClientMaster(clientCode, "A");
        } catch (Exception e) {
            log.error(e.getMessage());
            return null;
        }
    }

    public JsonNode getBranchMaster(String branchCode) {
        try {
            return korpClient.getBranchMaster(branchCode);
        } catch (Exception e) {
            log.error(e.getMessage());
            return null;
        }
    }

}
