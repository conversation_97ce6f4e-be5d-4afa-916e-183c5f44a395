package com.arihantcapital.oneclick.core.service;


import com.arihantcapital.oneclick.OneclickProperties;
import com.arihantcapital.oneclick.core.client.kra.cvl.pancheck.xml.CvlPancheckXmlClientImpl;
import com.arihantcapital.oneclick.enums.RandomNumberGenerationMethod;
import com.arihantcapital.oneclick.utils.RandomNumberUtils;
import com.fasterxml.jackson.databind.JsonNode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service("cvlPancheckXmlService")
public class CvlPancheckXmlService {
    private final OneclickProperties.KraConfig.CvlConfig.PancheckConfig.XmlConfig config;

    private final CvlPancheckXmlClientImpl cvlPancheckXmlClient;

    public CvlPancheckXmlService(OneclickProperties.KraConfig.CvlConfig.PancheckConfig.XmlConfig config) {
        this.config = config;
        CvlPancheckXmlClientImpl.ClientConfig clientConfig = new CvlPancheckXmlClientImpl.ClientConfig(config.baseUrl(), config.username(), config.password(), config.posCode(), config.rtaCode());
        this.cvlPancheckXmlClient = new CvlPancheckXmlClientImpl(clientConfig);
        log.info("KRA Pancheck JSON Service initialized with client config: {}", clientConfig);
    }

    // Your KRA Pancheck XML service methods here
    public JsonNode getToken(String passKey) {
        try {
            log.info("Initiating GetToken for passKey: {}", passKey);
            if (passKey == null || passKey.isEmpty()) {
                passKey = RandomNumberUtils.generateRequestId(RandomNumberGenerationMethod.UUID);
                log.info("PassKey is not provided! Generated new passKey: {}", passKey);
            }

            // Call Xml Client
            JsonNode token = cvlPancheckXmlClient.getToken(passKey);
            log.info("Token Response: {}", token);
            return token;
        } catch (Exception e) {
            log.error("Error fetching Token: {}", e.getMessage());
            return null;
        }
    }

    public JsonNode getPanStatus(String panNumber) {
        try {
            log.info("Initiating GetPanStatus Xml for panNumber: {}", panNumber);

            // Call Xml Client
            JsonNode panStatus = cvlPancheckXmlClient.getPanStatus(panNumber);
            log.info("Pan Status Response: {}", panStatus);

            return panStatus;
        } catch (Exception e) {
            log.error("Error fetching Pan Status: {}", e.getMessage());
            return null;
        }
    }

    public JsonNode solicitPANDetailsFetchALLKRA(String panNumber, String kraCode, String dob, String fetchType) {
        try {
            //Call Xml Client
            JsonNode kraData = cvlPancheckXmlClient.solicitPANDetailsFetchALLKRA(fetchType, kraCode, panNumber, dob);
            log.info("KRA Data Response: {}", kraData);

            return kraData;
        } catch (Exception e) {
            log.error("Error fetching KRA Data: {}", e.getMessage());
            return null;
        }
    }

    public JsonNode insertUpdateKYCRecord(JsonNode jsonValue) {
        return null;
    }
}