package com.arihantcapital.oneclick.core.service;

import com.arihantcapital.oneclick.OneclickProperties;
import com.arihantcapital.oneclick.core.client.arihant.msil.MsilClient;
import com.fasterxml.jackson.databind.JsonNode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service("msilService")
public class MsilService {
    private final OneclickProperties.MsilConfig config;
    private MsilClient msilClient;

    public MsilService(OneclickProperties.MsilConfig config) {
        this.config = config;
        MsilClient.ClientConfig clientConfig = new MsilClient.ClientConfig(config);
        MsilClient msilClient = new MsilClient(clientConfig);
        log.info("MSIL Client Details Service initialized with base URL: {}", config.baseUrl());
    }

    public String insertClientDetails(List<JsonNode> users) {
        try {
            return msilClient.insertClientDetails(users);
        } catch (Exception e) {
            log.error(e.getMessage());
            return null;
        }
    }

    public String insertClientDetails(JsonNode user) {
        try {
            return msilClient.insertClientDetails(user);
        } catch (Exception e) {
            log.error(e.getMessage());
            return null;
        }
    }

    public String updateClientDetails(List<JsonNode> users) {
        try {
            return msilClient.updateClientDetails(users);
        } catch (Exception e) {
            log.error(e.getMessage());
            return null;
        }
    }

    public String updateClientDetails(JsonNode user) {
        try {
            return msilClient.updateClientDetails(user);
        } catch (Exception e) {
            log.error(e.getMessage());
            return null;
        }
    }
}
