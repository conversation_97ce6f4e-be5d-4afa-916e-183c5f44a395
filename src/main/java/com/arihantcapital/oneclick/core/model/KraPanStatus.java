package com.arihantcapital.oneclick.core.model;

import lombok.Data;
import org.springframework.data.mongodb.core.index.IndexDirection;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.Date;

@Data
@Document(collection = "kra_pan_status")
public class KraPanStatus {
    private static final ZoneId INDIA_ZONE = ZoneId.of("Asia/Kolkata");

    @Indexed(unique = true)
    @Field(value = "appPanNo")
    private String appPanNo;

    @Field(value = "appCorAddProof")
    private String appCorAddProof;

    @Indexed
    @Field(value = "appEntryDt")
    private Date appEntryDt;

    @Field(value = "appHoldDeactiveRmks")
    private String appHoldDeactiveRmks;

    @Indexed
    @Field(value = "appIpvFlag")
    private String appIpvFlag;

    @Indexed
    @Field(value = "appKycMode")
    private String appKycMode;

    @Indexed(direction = IndexDirection.DESCENDING)
    @Field(value = "appModDt")
    private Date appModDt;

    @Indexed
    @Field(value = "appName")
    private String appName;

    @Field(value = "appPerAddProof")
    private String appPerAddProof;

    @Indexed
    @Field(value = "appStatus")
    private String appStatus;

    @Field(value = "appStatusDelta")
    private String appStatusDelta;

    @Field(value = "appStatusMessage")
    private String appStatusMessage;

    @Indexed
    @Field(value = "appStatusDt")
    private Date appStatusDt;

    @Field(value = "appUboFlag")
    private String appUboFlag;

    @Field(value = "appUpdtRmks")
    private String appUpdtRmks;

    @Indexed
    @Field(value = "appUpdtStatus")
    private String appUpdtStatus;

    @Indexed
    @Field(value = "kraCode")
    private String kraCode;


    public void setAppEntryDt(ZonedDateTime zonedDateTime) {
        this.appEntryDt = Date.from(zonedDateTime.toInstant());
    }

    public ZonedDateTime getAppEntryDtAsZonedDateTime() {
        return appEntryDt.toInstant().atZone(INDIA_ZONE);
    }

    public void setAppModDt(ZonedDateTime zonedDateTime) {
        this.appModDt = Date.from(zonedDateTime.toInstant());
    }

    public ZonedDateTime getAppModDtAsZonedDateTime() {
        return appModDt.toInstant().atZone(INDIA_ZONE);
    }

    public void setAppStatusDt(ZonedDateTime zonedDateTime) {
        this.appStatusDt = Date.from(zonedDateTime.toInstant());
    }

    public ZonedDateTime getAppStatusDtAsZonedDateTime(ZonedDateTime zonedDateTime) {
        return appStatusDt.toInstant().atZone(INDIA_ZONE);
    }

}
