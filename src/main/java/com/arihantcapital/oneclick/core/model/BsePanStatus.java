package com.arihantcapital.oneclick.core.model;

import lombok.Data;
import org.springframework.data.mongodb.core.index.IndexDirection;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.Date;

@Data
@Document(collection = "bse_pan_status")
public class BsePanStatus {
    private static final ZoneId INDIA_ZONE = ZoneId.of("Asia/Kolkata");

    @Indexed(unique = true)
    @Field(value = "clientCode")
    private String clientCode;

    @Indexed(direction = IndexDirection.DESCENDING)
    @Field(value = "clientName")
    private String clientName;

    @Indexed
    @Field(value = "aadhaarSeedFlag")
    private String aadhaarSeedFlag;

    @Indexed(direction = IndexDirection.DESCENDING)
    @Field(value = "createdOn")
    private Date createdOn;

    @Indexed
    @Field(value = "nameOnPanCard")
    private String nameOnPanCard;

    @Indexed
    @Field(value = "panName")
    private String panName;

    @Indexed
    @Field(value = "panNumber")
    private String panNumber;

    @Indexed
    @Field(value = "panValidationOn")
    private String panValidationOn;

    @Indexed
    @Field(value = "panStatus")
    private String panStatus;


    public void setCreatedOn(ZonedDateTime zonedDateTime) {
        this.createdOn = Date.from(zonedDateTime.toInstant());
    }

    public ZonedDateTime getCreatedOnAsZonedDateTime() {
        return createdOn.toInstant().atZone(INDIA_ZONE);
    }
}
