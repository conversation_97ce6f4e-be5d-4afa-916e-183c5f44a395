package com.arihantcapital.oneclick.core.model;

import lombok.Data;
import org.springframework.data.mongodb.core.index.IndexDirection;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import java.time.LocalDate;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.Date;
import java.util.List;

@Data
@Document(collection = "nse_pan_status")
public class NsePanStatus {

    private static final ZoneId INDIA_ZONE = ZoneId.of("Asia/Kolkata");

    @Field(value = "memberCode")
    private String memberCode;

    @Indexed(unique = true)
    @Field(value = "clientCode")
    private String clientCode;

    @Indexed(direction = IndexDirection.DESCENDING)
    @Field(value = "clientName")
    private String clientName;

    @Field(value = "clientNameDesc")
    private String clientNameDesc;

    @Indexed
    @Field(value = "adhrSeedingStatus")
    private String adhrSeedingStatus;

    @Indexed
    @Field(value = "pan")
    private String pan;

    @Indexed
    @Field(value = "dobOrDoi")
    private Date dobOrDoi;

    @Indexed
    @Field(value = "panNameMatchingStatus")
    private String panNameMatchingStatus;

    @Indexed
    @Field(value = "panDobMatchingStatus")
    private String panDobMatchingStatus;

    @Field(value = "uccStatus")
    private List<UccStatus> uccStatus;

    @Data
    public static class UccStatus {

        @Field(value = "segment")
        private String segment;

        @Indexed(direction = IndexDirection.DESCENDING)
        @Field(value = "creationOn")
        private Date creationOn;

        @Indexed
        @Field(value = "panStatus")
        private String panStatus;

        @Indexed
        @Field(value = "panStatusDate")
        private Date panStatusDate;

        @Indexed
        @Field(value = "exchangeStatus")
        private String exchangeStatus;

        public void setCreationOn(ZonedDateTime zonedDateTime) {
            this.creationOn = Date.from(zonedDateTime.toInstant());
        }

        public ZonedDateTime getCreationOnAsZonedDateTime() {
            return creationOn.toInstant().atZone(INDIA_ZONE);
        }

        public void setPanStatusDate(ZonedDateTime zonedDateTime) {
            this.panStatusDate = Date.from(zonedDateTime.toInstant());
        }

        public ZonedDateTime getPanStatusDateAsZonedDateTime() {
            return creationOn.toInstant().atZone(INDIA_ZONE);
        }
    }

    // Convert from LocalDate using India timezone
    public void setDobOrDoi(LocalDate localDate) {
        this.dobOrDoi = Date.from(localDate.atStartOfDay(INDIA_ZONE).toInstant());
    }

    // Convert to LocalDate using India timezone
    public LocalDate getDobOrDoiAsLocalDate() {
        return dobOrDoi.toInstant().atZone(INDIA_ZONE).toLocalDate();
    }

}
