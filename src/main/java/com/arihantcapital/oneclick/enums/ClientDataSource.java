package com.arihantcapital.oneclick.enums;

import lombok.Getter;

@Getter
public enum ClientDataSource {
    KORP("KORP", "Korp Backoffice Service "), LOCAL("LOCAL", "Oneclick Database");

    private final String code;
    private final String description;

    ClientDataSource(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public static ClientDataSource fromCode(String code) {
        for (ClientDataSource clientDataSource : ClientDataSource.values()) {
            if (clientDataSource.getCode().equals(code)) {
                return clientDataSource;
            }
        }
        throw new IllegalArgumentException("Invalid data source code: " + code);
    }
}
