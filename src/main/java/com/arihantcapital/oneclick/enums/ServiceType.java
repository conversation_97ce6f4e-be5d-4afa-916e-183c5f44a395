package com.arihantcapital.oneclick.enums;

public enum ServiceType {

    USER_SERVICE("USER_SERVICE", "User Service"),
    ORDER_SERVICE("ORDER_SERVICE", "Order Service"),
    NOTIFICATION_SERVICE("NOTIFICATION_SERVICE", "Notification Service"),
    PAYMENT_SERVICE("PAYMENT_SERVICE", "Payment Service"),


    KORP_SERVICE("KORP_SERVICE", "KORP Service"),
    MSIL_SERVICE("MSIL_SERVICE", "MSIL Service"),
    NSE_UCC_SERVICE("NSE_UCC_SERVICE", "NSE UCC Service"),
    BSE_UCC_SERVICE("BSE_UCC_SERVICE", "BSE UCC Service"),
    BSE_STAR_MUTUAL_FUND_SERVICE("BSE_STAR_MUTUAL_FUND_SERVICE", "BSE Star Mutual Fund Service"),
    CKYC_SERVICE("CKYC_SERVICE", "CKYC Service"),
    CVL_OTP_SERVICE("CVL_OTP_SERVICE", "CVL OTP Service"),
    CVL_PANCHECK_XML_SERVICE("CVL_PANCHECK_XML_SERVICE", "CVL Pancheck XML Service"),
    CVL_PANCHECK_JSON_SERVICE("CVL_PANCHECK_JSON_SERVICE", "CVL Pancheck JSON Service"),
    CVL_SFTP_SERVICE("CVL_SFTP_SERVICE", "CVL SFTP Service"),
    ODIN_UUM_SERVICE("ODIN_UUM_SERVICE", "ODIN UUM Service"),
    OMNESYS_SERVICE("OMNESYS_SERVICE", "Omnesys Service");

    private final String code;
    private final String description;

    ServiceType(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public static ServiceType fromCode(String code) {
        for (ServiceType serviceType : ServiceType.values()) {
            if (serviceType.getCode().equals(code)) {
                return serviceType;
            }
        }
        throw new IllegalArgumentException("Invalid service type code: " + code);
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

}
