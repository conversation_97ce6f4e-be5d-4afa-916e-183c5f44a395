package com.arihantcapital.oneclick.model;

import lombok.Data;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import java.util.List;

@Data
@Document(collection = "client")
public class Client {

    @Indexed
    @Field(value = "kycMode")
    private String kycMode;

    @Indexed
    @Field(value = "clientCode")
    private String clientCode;

    @Field(value = "clientName")
    private String clientName;

    @Indexed
    @Field(value = "panNumber")
    private String panNumber;

    @Field(value = "nameAsPerPan")
    private String nameAsPerPan;

    @Indexed
    @Field(value = "clientType")
    private String clientType;

    @Field(value = "clientSubType")
    private String clientSubType;

    @Indexed
    @Field(value = "branchCode")
    private String branchCode;

    @Field(value = "groupCode")
    private String groupCode;

    @Indexed
    @Field(value = "email")
    private String email;

    @Indexed
    @Field(value = "dobOrDoi")
    private String dobOrDoi;

    @Indexed
    @Field(value = "gender")
    private String gender;

    @Field(value = "maritalStatus")
    private String maritalStatus;

    @Field(value = "fatherOrSpouseName")
    private String fatherOrSpouseName;

    @Indexed
    @Field(value = "kraCode")
    private String kraCode;

    @Field(value = "segments")
    private String segments;

    @Indexed
    @Field(value = "clientStatus")
    private String clientStatus;

    @Field(value = "optedForUpi")
    private String optedForUpi;

    @Field(value = "ddpi")
    private String ddpi;

    @Field(value = "ddpiDate")
    private String ddpiDate;

    @Field(value = "poa")
    private String poa;

    @Field(value = "poaDate")
    private String poaDate;

    @Field(value = "agreementDate")
    private String agreementDate;

    @Field(value = "ckycRefNo")
    private String ckycRefNo;

    @Field(value = "pep")
    private String pep;

    @Field(value = "income")
    private String income;

    @Field(value = "incomeDate")
    private String incomeDate;

    @Field(value = "addressProof")
    private String addressProof;

    @Field(value = "addressProofRefNo")
    private String addressProofRefNo;

    @Field(value = "address1")
    private String address1;

    @Field(value = "address2")
    private String address2;

    @Field(value = "address3")
    private String address3;

    @Field(value = "city")
    private String city;

    @Indexed
    @Field(value = "state")
    private String state;

    @Field(value = "country")
    private String country;

    @Field(value = "pincode")
    private String pincode;

    @Field(value = "nationality")
    private String nationality;

    @Field(value = "residentialStatus")
    private String residentialStatus;

    @Indexed
    @Field(value = "occupation")
    private String occupation;

    @Indexed
    @Field(value = "incomeRange")
    private String incomeRange;

    @Field(value = "nominees")
    private List<String> nominees;

    @Field(value = "banks")
    private List<String> banks;

    @Field(value = "depositories")
    private List<String> depositories;
}
