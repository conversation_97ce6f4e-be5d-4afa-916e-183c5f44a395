package com.arihantcapital.oneclick.model;


import lombok.Data;
import org.springframework.data.mongodb.core.mapping.Document;

@Data
@Document(collection = "client_bank")
public class ClientBank {
    private String clientCode;
    private String bankName;
    private String bankBranch;
    private String bankAccountType;
    private String bankAccountNumber;
    private String bankIfscCode;
    private String bankMicrCode;
    private String bankSwiftCode;
    private String bankCode;
    private String bankAddress;
    private String defaultBank;
}
