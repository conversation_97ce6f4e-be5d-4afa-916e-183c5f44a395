package com.arihantcapital.oneclick.model;

import lombok.Data;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

@Data
@Document(collection = "client_depository")
public class ClientDepository {

    @Indexed
    @Field(value = "clientCode")
    private String clientCode;

    @Indexed
    @Field(value = "depositoryName")
    private String depositoryName;

    @Indexed
    @Field(value = "depositoryId")
    private String depositoryId;

    @Indexed
    @Field(value = "depositoryType")
    private String depositoryType;

    @Indexed(unique = true)
    @Field(value = "depositoryClientId")
    private String depositoryClientId;

    @Indexed
    @Field(value = "depositoryStatus")
    private String depositoryStatus;

    @Indexed
    @Field(value = "ddpi")
    private String ddpi;

    @Indexed
    @Field(value = "poa")
    private String poa;
}
