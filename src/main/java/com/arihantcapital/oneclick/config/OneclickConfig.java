package com.arihantcapital.oneclick.config;


import com.arihantcapital.oneclick.OneclickProperties;
import com.arihantcapital.oneclick.core.service.*;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.io.IOException;
import java.security.KeyStoreException;
import java.security.NoSuchAlgorithmException;
import java.security.UnrecoverableEntryException;
import java.security.cert.CertificateException;

@Slf4j
@Configuration
@EnableConfigurationProperties(OneclickProperties.class)
public class OneclickConfig {

    private final OneclickProperties properties;

    public OneclickConfig(OneclickProperties properties) {
        this.properties = properties;
    }

    @PostConstruct
    public void logConfiguration() {
        log.info("OneClick Configuration Loaded");
        log.info("=============================");

        log.info("KORP Service: {}", properties.isKorpEnabled() ? "ENABLED" : "DISABLED");
        if (properties.isKorpEnabled()) {
            OneclickProperties.KorpConfig config = properties.korp();

            log.info("  - Base URL: {}", config.baseUrl());
            log.info("  - Username: {}", config.username());
            log.info("  - Password: {}", config.password());
            log.info("  - Grant Type: {}", config.grantType());
        }

        log.info("MSIL Service: {}", properties.isMsilEnabled() ? "ENABLED" : "DISABLED");
        if (properties.isMsilEnabled()) {
            OneclickProperties.MsilConfig config = properties.msil();

            log.info("  - Base URL: {}", config.baseUrl());
            log.info("  - Token: {}", config.token());
        }

        log.info("NSE UCC Service: {}", properties.isNseUccEnabled() ? "ENABLED" : "DISABLED");
        if (properties.isNseUccEnabled()) {
            OneclickProperties.NseUccConfig config = properties.nseUcc();

            log.info("  - Base URL: {}", config.baseUrl());
            log.info("  - Username: {}", config.username());
            log.info("  - Password: {}", config.password());
            log.info("  - API Name: {}", config.apiName());
        }


        log.info("BSE UCC Service: {}", properties.isBseUccEnabled() ? "ENABLED" : "DISABLED");
        if (properties.isBseUccEnabled()) {
            OneclickProperties.BseUccConfig config = properties.bseUcc();

            log.info("  - API Version: {}", config.apiVersion());
            log.info("  - Base URL: {}", config.baseUrl());
            log.info("  - Member Code: {}", config.memberCode());
            log.info("  - Username: {}", config.username());
            log.info("  - Password: {}", config.password());
            log.info("  - Public Key XML: {}", config.publicKeyXml());
        }

        log.info("BSE Star Mutual Fund Service: {}", properties.isBseStarMutualFundEnabled() ? "ENABLED" : "DISABLED");
        if (properties.isBseStarMutualFundEnabled()) {
            OneclickProperties.BseStarMutualFundConfig config = properties.bseStarMutualFund();

            log.info("  - Base URL: {}", config.baseUrl());
            log.info("  - Member Code: {}", config.memberCode());
            log.info("  - Username: {}", config.username());
            log.info("  - Password: {}", config.password());
        }


        // Log CKYC Configuration
        log.info("CKYC Service: {}", properties.isCkycEnabled() ? "ENABLED" : "DISABLED");
        if (properties.isCkycEnabled()) {
            OneclickProperties.CkycConfig config = properties.ckyc();

            log.info("  - API Version: {}", config.apiVersion());
            log.info("  - FI Code: {}", config.fiCode());
            log.info("  - Base URL: {}", config.baseUrl());
            log.info("  - Key Store Type: {}", config.keyStoreType());
            log.info("  - Cersai Public Key File Path: {}", config.cersaiPublicKeyFilePath());
            log.info("  - FI Certificate Key Store File Path: {}", config.fiCertificateKeyStoreFilePath());
            log.info("  - FI Certificate Key Store File Password: {}", config.fiCertificateKeyStoreFilePassword());
            log.info("  - FI Certificate Private Key Alias: {}", config.fiCertificatePrivateKeyAlias());
        }

        // Log KRA OTP Configuration
        log.info("KRA OTP Service: {}", properties.isKraOtpEnabled() ? "ENABLED" : "DISABLED");
        if (properties.isKraOtpEnabled()) {
            OneclickProperties.KraConfig.CvlConfig.OtpConfig config = properties.kra().cvl().otp();

            log.info("  - Base URL: {}", config.baseUrl());
            log.info("  - Username: {}", config.username());
            log.info("  - Password: {}", config.password());

        }


        // Log KRA Pancheck XML Configuration
        log.info("KRA Pancheck XML Service: {}", properties.isKraPancheckXmlEnabled() ? "ENABLED" : "DISABLED");
        if (properties.isKraPancheckXmlEnabled()) {
            OneclickProperties.KraConfig.CvlConfig.PancheckConfig.XmlConfig config = properties.kra().cvl().pancheck().xml();

            log.info("  - Base URL: {}", config.baseUrl());
            log.info("  - Username: {}", config.username());
            log.info("  - Password: {}", config.password());
            log.info("  - POS Code: {}", config.posCode());
            log.info("  - RTA Code: {}", config.rtaCode());
        }

        // Log KRA Pancheck JSON Configuration
        log.info("KRA Pancheck JSON Service: {}", properties.isKraPancheckJsonEnabled() ? "ENABLED" : "DISABLED");
        if (properties.isKraPancheckJsonEnabled()) {
            OneclickProperties.KraConfig.CvlConfig.PancheckConfig.JsonConfig config = properties.kra().cvl().pancheck().json();

            log.info("  - User Agent: {}", config.userAgent());
            log.info("  - Base URL: {}", config.baseUrl());
            log.info("  - API Key: {}", config.apiKey());
            log.info("  - AES Key: {}", config.aesKey());
            log.info("  - Username: {}", config.username());
            log.info("  - Password: {}", config.password());
            log.info("  - POS Code: {}", config.posCode());
            log.info("  - RTA Code: {}", config.rtaCode());
            log.info("  - Token Valid Time: {}", config.tokenValidTime());
        }

        log.info("KRA CVL SFTP Service: {}", properties.isCvlKraSftpEnabled() ? "ENABLED" : "DISABLED");
        if (properties.isCvlKraSftpEnabled()) {
            OneclickProperties.KraConfig.CvlConfig.SftpConfig config = properties.kra().cvl().sftp();

            log.info("  - Host: {}", config.host());
            log.info("  - Port: {}", config.port());
            log.info("  - Username: {}", config.username());
            log.info("  - Password: {}", config.password());
        }

        log.info("ODIN UUM Service: {}", properties.isOdinUumEnabled() ? "ENABLED" : "DISABLED");
        if (properties.isOdinUumEnabled()) {
            OneclickProperties.OdinUumConfig config = properties.odinUum();

            log.info("  - Base URL: {}", config.baseUrl());
            log.info("  - Username: {}", config.username());
            log.info("  - Password: {}", config.password());
            log.info("  - IP Address: {}", config.ipAddress());
        }


        log.info("=============================");
    }


    // Conditional beans - only created if services are enabled

    @Bean
    @ConditionalOnProperty(name = "oneclick.korp.enabled", havingValue = "true")
    public KorpService korpService() {
        log.info("Creating KORP Service Bean");
        return new KorpService(properties.korp());
    }

    @Bean
    @ConditionalOnProperty(name = "oneclick.msil.enabled", havingValue = "true")
    public MsilService msilService() {
        log.info("Creating MSIL Service Bean");
        return new MsilService(properties.msil());
    }

    @Bean
    @ConditionalOnProperty(name = "oneclick.nse-ucc.enabled", havingValue = "true")
    public NseUccService nseUccService() {
        log.info("Creating NSE UCC Service Bean");
        return new NseUccService(properties.nseUcc());
    }

    @Bean
    @ConditionalOnProperty(name = "oneclick.bse-ucc.enabled", havingValue = "true")
    public BseUccService bseUccService() {
        log.info("Creating BSE UCC Service Bean");
        return new BseUccService(properties.bseUcc());
    }

    @Bean
    @ConditionalOnProperty(name = "oneclick.bse-star-mutual-fund.enabled", havingValue = "true")
    public BseStarMutualFundService bseStarMutualFundService() {
        log.info("Creating BSE Star Mutual Fund Service Bean");
        return new BseStarMutualFundService(properties.bseStarMutualFund());
    }

    @Bean
    @ConditionalOnProperty(name = "oneclick.ckyc.enabled", havingValue = "true")
    public CkycVerificationService ckycService() throws UnrecoverableEntryException, CertificateException, KeyStoreException, IOException, NoSuchAlgorithmException {
        log.info("Creating CKYC Service Bean");
        return new CkycVerificationService(properties.ckyc());
    }

    @Bean
    @ConditionalOnProperty(name = "oneclick.kra.cvl.otp.enabled", havingValue = "true")
    public CvlOtpService cvlOtpService() {
        log.info("Creating KRA OTP Service Bean");
        return new CvlOtpService(properties.kra().cvl().otp());
    }

    @Bean
    @ConditionalOnProperty(name = "oneclick.kra.cvl.pancheck.xml.enabled", havingValue = "true")
    public CvlPancheckXmlService cvlPancheckXmlService() {
        log.info("Creating KRA Pancheck XML Service Bean");
        return new CvlPancheckXmlService(properties.kra().cvl().pancheck().xml());
    }

    @Bean
    @ConditionalOnProperty(name = "oneclick.kra.cvl.pancheck.json.enabled", havingValue = "true")
    public CvlPancheckJsonService cvlPancheckJsonService() {
        log.info("Creating KRA Pancheck JSON Service Bean");
        return new CvlPancheckJsonService(properties.kra().cvl().pancheck().json());
    }

    @Bean
    @ConditionalOnProperty(name = "oneclick.kra.cvl.sftp.enabled", havingValue = "true")
    public CvlSftpService cvlSftpService() {
        log.info("Creating KRA CVL SFTP Service Bean");
        return new CvlSftpService(properties.kra().cvl().sftp());
    }

    @Bean
    @ConditionalOnProperty(name = "oneclick.odin-uum.enabled", havingValue = "true")
    public OdinUumService odinUumService() {
        log.info("Creating ODIN UUM Service Bean");
        return new OdinUumService(properties.odinUum());
    }
}
