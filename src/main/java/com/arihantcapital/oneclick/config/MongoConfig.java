package com.arihantcapital.oneclick.config;

import com.mongodb.client.MongoClients;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.convert.ConverterBuilder;
import org.springframework.data.mongodb.config.AbstractMongoClientConfiguration;
import org.springframework.data.mongodb.config.EnableMongoAuditing;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.SimpleMongoClientDatabaseFactory;
import org.springframework.data.mongodb.core.convert.MongoCustomConversions;

import java.time.LocalDate;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

@Configuration
@EnableMongoAuditing
public class MongoConfig extends AbstractMongoClientConfiguration {

    private static final ZoneId INDIA_ZONE = ZoneId.of("Asia/Kolkata");

    @Value("${spring.data.mongodb.uri}")
    private String mongoUri;


    @Override
    protected String getDatabaseName() {
        return "oneclick";
    }

    /**
     * Default MongoTemplate for database
     */
    @Bean(name = "mongoTemplate")
    public MongoTemplate mongoTemplate() {
        return new MongoTemplate(new SimpleMongoClientDatabaseFactory(MongoClients.create(mongoUri), getDatabaseName()));
    }

    @Bean
    @Override
    public MongoCustomConversions customConversions() {
        List<?> converters = Arrays.asList(

                // LocalDate Converters
                ConverterBuilder.writing(LocalDate.class, Date.class, source -> {
                    return Date.from(source.atStartOfDay(INDIA_ZONE).toInstant());
                }),
                ConverterBuilder.reading(Date.class, LocalDate.class, source -> {
                    return source.toInstant().atZone(INDIA_ZONE).toLocalDate();
                }),

                // ZonedDateTime Converters
                ConverterBuilder.writing(ZonedDateTime.class, Date.class, source -> {
                    return Date.from(source.toInstant());
                }),
                ConverterBuilder.reading(Date.class, ZonedDateTime.class, source -> {
                    return source.toInstant().atZone(INDIA_ZONE);
                })
        );
        return new MongoCustomConversions(converters);
    }
}


