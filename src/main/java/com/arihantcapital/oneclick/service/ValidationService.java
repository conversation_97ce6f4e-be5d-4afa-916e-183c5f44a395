package com.arihantcapital.oneclick.service;

import com.arihantcapital.oneclick.enums.ServiceType;
import com.arihantcapital.oneclick.validation.CommonData;
import com.arihantcapital.oneclick.validation.DataValidator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Service
public class ValidationService {
    @Autowired
    private DataValidator dataValidator;

    public ValidationResult validateForService(CommonData data, ServiceType serviceType) {
        Map<String, List<String>> errors = dataValidator.validate(data, serviceType);
        return new ValidationResult(errors.isEmpty(), errors);
    }

    public void validateAndThrowForService(CommonData data, ServiceType serviceType) {
        dataValidator.validateAndThrow(data, serviceType);
    }

    public static class ValidationResult {
        private final boolean valid;
        private final Map<String, List<String>> errors;

        public ValidationResult(boolean valid, Map<String, List<String>> errors) {
            this.valid = valid;
            this.errors = errors;
        }

        public boolean isValid() {
            return valid;
        }

        public Map<String, List<String>> getErrors() {
            return errors;
        }
    }
}
