package com.arihantcapital.oneclick.service;

import com.arihantcapital.oneclick.enums.ServiceType;
import com.arihantcapital.oneclick.validation.generic.GenericDataValidator;
import com.arihantcapital.oneclick.validation.generic.GenericFieldValidationConfig;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Service
public class GenericValidationService {
    @Autowired
    private GenericDataValidator dataValidator;

    public <T> ValidationResult validateForService(T data, ServiceType serviceType) {
        Map<String, List<String>> errors = dataValidator.validate(data, serviceType);
        return new ValidationResult(errors.isEmpty(), errors);
    }

    public <T> void validateAndThrowForService(T data, ServiceType serviceType) {
        dataValidator.validateAndThrow(data, serviceType);
    }

    // Method to register custom validation configurations
    public <T> void registerValidationConfiguration(Class<T> clazz,
                                                    Map<String, GenericFieldValidationConfig<T>> config) {
        dataValidator.registerValidationConfiguration(clazz, config);
    }

    public static class ValidationResult {
        private final boolean valid;
        private final Map<String, List<String>> errors;

        public ValidationResult(boolean valid, Map<String, List<String>> errors) {
            this.valid = valid;
            this.errors = errors;
        }

        public boolean isValid() {
            return valid;
        }

        public Map<String, List<String>> getErrors() {
            return errors;
        }
    }
}
