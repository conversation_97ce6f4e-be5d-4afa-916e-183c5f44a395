package com.arihantcapital.oneclick.validation;

import com.arihantcapital.oneclick.enums.ServiceType;

import java.util.HashMap;
import java.util.Map;

public class FieldValidationConfig {
    private final String fieldName;
    private final Map<ServiceType, ValidationRule> serviceRules;

    public FieldValidationConfig(String fieldName) {
        this.fieldName = fieldName;
        this.serviceRules = new HashMap<>();
    }

    public FieldValidationConfig addRule(ServiceType service, ValidationRule rule) {
        this.serviceRules.put(service, rule);
        return this;
    }

    public ValidationRule getRule(ServiceType service) {
        return serviceRules.get(service);
    }

    public String getFieldName() {
        return fieldName;
    }
}
