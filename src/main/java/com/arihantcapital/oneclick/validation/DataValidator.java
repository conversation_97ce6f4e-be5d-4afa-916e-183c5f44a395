package com.arihantcapital.oneclick.validation;

import com.arihantcapital.oneclick.enums.ServiceType;
import org.springframework.stereotype.Component;

import java.lang.reflect.Field;
import java.util.*;
import java.util.regex.Pattern;

@Component
public class DataValidator {
    private final Map<String, FieldValidationConfig> fieldConfigs;

    public DataValidator() {
        this.fieldConfigs = new HashMap<>();
        setupValidationRules();
    }


    private void setupValidationRules() {

        // User ID validation
        fieldConfigs.put("userId", new FieldValidationConfig("userId")
                .addRule(ServiceType.USER_SERVICE, new ValidationRule()
                        .required(true)
                        .minLength(5)
                        .maxLength(20)
                        .regexPattern("^[a-zA-Z0-9_]+$")
                        .errorMessage("User ID must be 5-20 alphanumeric characters"))
                .addRule(ServiceType.ORDER_SERVICE, new ValidationRule()
                        .required(true)
                        .minLength(5)
                        .errorMessage("User ID is required for orders")));

        // Email validation
        fieldConfigs.put("email", new FieldValidationConfig("email")
                .addRule(ServiceType.USER_SERVICE, new ValidationRule()
                        .required(true)
                        .regexPattern("^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$")
                        .errorMessage("Valid email address is required"))
                .addRule(ServiceType.NOTIFICATION_SERVICE, new ValidationRule()
                        .required(true)
                        .regexPattern("^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$")
                        .errorMessage("Valid email required for notifications")));

        // Phone validation
        fieldConfigs.put("phone", new FieldValidationConfig("phone")
                .addRule(ServiceType.USER_SERVICE, new ValidationRule()
                        .required(false)
                        .regexPattern("^\\+?[\\d\\s\\-\\(\\)]{10,15}$")
                        .errorMessage("Phone must be 10-15 digits with optional formatting"))
                .addRule(ServiceType.NOTIFICATION_SERVICE, new ValidationRule()
                        .required(true)
                        .regexPattern("^\\+?[\\d\\s\\-\\(\\)]{10,15}$")
                        .errorMessage("Valid phone number required for SMS notifications")));

        // Name validation
        fieldConfigs.put("name", new FieldValidationConfig("name")
                .addRule(ServiceType.USER_SERVICE, new ValidationRule()
                        .required(true)
                        .minLength(2)
                        .maxLength(50)
                        .errorMessage("Name must be 2-50 characters"))
                .addRule(ServiceType.ORDER_SERVICE, new ValidationRule()
                        .required(true)
                        .minLength(2)
                        .errorMessage("Customer name required for orders")));

        // Amount validation
        fieldConfigs.put("amount", new FieldValidationConfig("amount")
                .addRule(ServiceType.ORDER_SERVICE, new ValidationRule()
                        .required(true)
                        .customValidator(value -> value != null && ((Double) value) > 0)
                        .errorMessage("Amount must be greater than 0"))
                .addRule(ServiceType.PAYMENT_SERVICE, new ValidationRule()
                        .required(true)
                        .customValidator(value -> value != null && ((Double) value) > 0 && ((Double) value) <= 10000)
                        .errorMessage("Amount must be between 0 and 10,000")));

        // Currency validation
        List<Object> currencies = Arrays.asList("USD", "EUR", "GBP", "INR");
        fieldConfigs.put("currency", new FieldValidationConfig("currency")
                .addRule(ServiceType.ORDER_SERVICE, new ValidationRule()
                        .required(true)
                        .allowedValues(currencies)
                        .errorMessage("Currency must be USD, EUR, GBP, or INR"))
                .addRule(ServiceType.PAYMENT_SERVICE, new ValidationRule()
                        .required(true)
                        .allowedValues(currencies)
                        .errorMessage("Valid currency required for payment")));

        // Status validation
        fieldConfigs.put("status", new FieldValidationConfig("status")
                .addRule(ServiceType.ORDER_SERVICE, new ValidationRule()
                        .allowedValues(Arrays.asList("pending", "processing", "shipped", "delivered", "cancelled"))
                        .errorMessage("Invalid order status"))
                .addRule(ServiceType.PAYMENT_SERVICE, new ValidationRule()
                        .allowedValues(Arrays.asList("pending", "completed", "failed", "refunded"))
                        .errorMessage("Invalid payment status")));
    }

    private List<String> validateField(String fieldName, Object value, ValidationRule rule) {
        List<String> errors = new ArrayList<>();

        // Check if field is required
        if (rule.isRequired() && (value == null || value.toString().trim().isEmpty())) {
            errors.add(rule.getErrorMessage() != null ? rule.getErrorMessage() : fieldName + " is required");
            return errors;
        }

        // Skip other validations if value is null/empty and not required
        if (value == null || value.toString().trim().isEmpty()) {
            return errors;
        }

        String stringValue = value.toString();

        // Length validations
        if (rule.getMinLength() != null && stringValue.length() < rule.getMinLength()) {
            errors.add(rule.getErrorMessage() != null ? rule.getErrorMessage() :
                    fieldName + " must be at least " + rule.getMinLength() + " characters");
        }

        if (rule.getMaxLength() != null && stringValue.length() > rule.getMaxLength()) {
            errors.add(rule.getErrorMessage() != null ? rule.getErrorMessage() :
                    fieldName + " must be at most " + rule.getMaxLength() + " characters");
        }

        // Regex validation
        if (rule.getRegexPattern() != null && !Pattern.matches(rule.getRegexPattern(), stringValue)) {
            errors.add(rule.getErrorMessage() != null ? rule.getErrorMessage() :
                    fieldName + " format is invalid");
        }

        // Allowed values validation
        if (rule.getAllowedValues() != null && !rule.getAllowedValues().contains(value)) {
            errors.add(rule.getErrorMessage() != null ? rule.getErrorMessage() :
                    fieldName + " must be one of: " + rule.getAllowedValues());
        }

        // Custom validator
        if (rule.getCustomValidator() != null && !rule.getCustomValidator().test(value)) {
            errors.add(rule.getErrorMessage() != null ? rule.getErrorMessage() :
                    fieldName + " failed custom validation");
        }

        return errors;
    }

    public Map<String, List<String>> validate(CommonData data, ServiceType serviceType) {
        Map<String, List<String>> validationErrors = new HashMap<>();

        for (Map.Entry<String, FieldValidationConfig> entry : fieldConfigs.entrySet()) {
            String fieldName = entry.getKey();
            FieldValidationConfig config = entry.getValue();
            ValidationRule rule = config.getRule(serviceType);

            if (rule != null) { // Only validate if service has rules for this field
                Object value = getFieldValue(data, fieldName);
                List<String> fieldErrors = validateField(fieldName, value, rule);
                if (!fieldErrors.isEmpty()) {
                    validationErrors.put(fieldName, fieldErrors);
                }
            }
        }

        return validationErrors;
    }

    public void validateAndThrow(CommonData data, ServiceType serviceType) throws ValidationError {
        Map<String, List<String>> errors = validate(data, serviceType);
        if (!errors.isEmpty()) {
            // Throw the first error found
            Map.Entry<String, List<String>> firstError = errors.entrySet().iterator().next();
            throw new ValidationError(firstError.getKey(), firstError.getValue().get(0));
        }
    }

    public boolean isValid(CommonData data, ServiceType serviceType) {
        return validate(data, serviceType).isEmpty();
    }

    private Object getFieldValue(CommonData data, String fieldName) {
        try {
            Field field = CommonData.class.getDeclaredField(fieldName);
            field.setAccessible(true);
            return field.get(data);
        } catch (NoSuchFieldException | IllegalAccessException e) {
            return null;
        }
    }
}
