package com.arihantcapital.oneclick.validation.generic;

import com.arihantcapital.oneclick.enums.ServiceType;
import com.arihantcapital.oneclick.service.GenericValidationService;
import jakarta.annotation.PostConstruct;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

@Component
public class UserProfileValidationConfiguration {

    @Autowired
    private GenericValidationService validationService;

    @PostConstruct
    public void setupUserProfileValidation() {
        Map<String, GenericFieldValidationConfig<UserProfile>> fieldConfigs = new HashMap<>();

        // Username validation - different rules for different services
        fieldConfigs.put("username", new GenericFieldValidationConfig<UserProfile>("username")
                .addRule(ServiceType.USER_SERVICE, new GenericValidationRule<UserProfile>()
                        .required(true)
                        .minLength(3)
                        .maxLength(20)
                        .regexPattern("^[a-zA-Z0-9_]+$")
                        .contextualValidator(context -> {
                            // Additional business logic: check if username is already taken
                            String username = context.getStringValue("username");
                            return username != null && !isUsernameTaken(username);
                        })
                        .errorMessage("Username must be 3-20 alphanumeric characters and unique")));

        // Age validation with context-dependent rules
        fieldConfigs.put("age", new GenericFieldValidationConfig<UserProfile>("age")
                .addRule(ServiceType.USER_SERVICE, new GenericValidationRule<UserProfile>()
                        .required(true)
                        .customValidator(value -> {
                            if (value instanceof Integer) {
                                int age = (Integer) value;
                                return age >= 13 && age <= 120;
                            }
                            return false;
                        })
                        .errorMessage("Age must be between 13 and 120")));

        // Bio validation - length depends on profile type
        fieldConfigs.put("bio", new GenericFieldValidationConfig<UserProfile>("bio")
                .addRule(ServiceType.USER_SERVICE, new GenericValidationRule<UserProfile>()
                        .contextualValidator(context -> {
                            String bio = context.getStringValue("bio");
                            String profileType = context.getStringValue("profileType");

                            if (bio == null || bio.trim().isEmpty()) {
                                return true; // Bio is optional
                            }

                            // Different length limits based on profile type
                            int maxLength = "PREMIUM".equals(profileType) ? 500 : 200;
                            return bio.length() <= maxLength;
                        })
                        .errorMessage("Bio length exceeds limit for your profile type")));

        // SSN validation - only required for certain profile types
        fieldConfigs.put("socialSecurityNumber", new GenericFieldValidationConfig<UserProfile>("socialSecurityNumber")
                .addRule(ServiceType.USER_SERVICE, new GenericConditionalValidationRule<UserProfile>()
                        .when(context -> "BUSINESS".equals(context.getStringValue("profileType")))
                        .required(true)
                        .regexPattern("^\\d{3}-\\d{2}-\\d{4}$")
                        .errorMessage("Valid SSN required for business profiles")));

        // Language validation with age-based restrictions
        fieldConfigs.put("preferredLanguage", new GenericFieldValidationConfig<UserProfile>("preferredLanguage")
                .addRule(ServiceType.USER_SERVICE, new GenericValidationRule<UserProfile>()
                        .contextualValidator(context -> {
                            String language = context.getStringValue("preferredLanguage");
                            Integer age = context.getIntegerValue("age");

                            if (language == null) return true; // Optional field

                            // Age-restricted languages
                            if (age != null && age < 18 && "ADULT_CONTENT_LANG".equals(language)) {
                                return false;
                            }

                            return Arrays.asList("EN", "ES", "FR", "DE", "IT", "ADULT_CONTENT_LANG")
                                    .contains(language);
                        })
                        .errorMessage("Invalid language or age-restricted language selected")));

        validationService.registerValidationConfiguration(UserProfile.class, fieldConfigs);
    }

    private boolean isUsernameTaken(String username) {
        // Simulate database check
        return "admin".equals(username) || "root".equals(username);
    }
}
