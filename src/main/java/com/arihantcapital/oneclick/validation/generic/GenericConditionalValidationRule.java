package com.arihantcapital.oneclick.validation.generic;

public class GenericConditionalValidationRule<T> extends GenericValidationRule<T> {
    private GenericContextualValidator<T> condition;

    public GenericConditionalValidationRule<T> when(GenericContextualValidator<T> condition) {
        this.condition = condition;
        return this;
    }

    public boolean shouldApply(GenericValidationContext<T> context) {
        return condition == null || condition.validate(context);
    }

    public GenericContextualValidator<T> getCondition() {
        return condition;
    }
}
