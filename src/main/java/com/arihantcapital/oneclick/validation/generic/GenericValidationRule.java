package com.arihantcapital.oneclick.validation.generic;

import java.util.List;
import java.util.function.Predicate;

public class GenericValidationRule<T> {
    private boolean required = false;
    private Integer minLength;
    private Integer maxLength;
    private String regexPattern;
    private List<Object> allowedValues;
    private Predicate<Object> customValidator;
    private GenericContextualValidator<T> contextualValidator;
    private String errorMessage;

    // Constructors
    public GenericValidationRule() {
    }

    public GenericValidationRule(boolean required) {
        this.required = required;
    }

    // Builder pattern methods
    public GenericValidationRule<T> required(boolean required) {
        this.required = required;
        return this;
    }

    public GenericValidationRule<T> minLength(Integer minLength) {
        this.minLength = minLength;
        return this;
    }

    public GenericValidationRule<T> maxLength(Integer maxLength) {
        this.maxLength = maxLength;
        return this;
    }

    public GenericValidationRule<T> regexPattern(String regexPattern) {
        this.regexPattern = regexPattern;
        return this;
    }

    public GenericValidationRule<T> allowedValues(List<Object> allowedValues) {
        this.allowedValues = allowedValues;
        return this;
    }

    public GenericValidationRule<T> customValidator(Predicate<Object> customValidator) {
        this.customValidator = customValidator;
        return this;
    }

    public GenericValidationRule<T> contextualValidator(GenericContextualValidator<T> contextualValidator) {
        this.contextualValidator = contextualValidator;
        return this;
    }

    public GenericValidationRule<T> errorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
        return this;
    }

    // Getters
    public boolean isRequired() {
        return required;
    }

    public Integer getMinLength() {
        return minLength;
    }

    public Integer getMaxLength() {
        return maxLength;
    }

    public String getRegexPattern() {
        return regexPattern;
    }

    public List<Object> getAllowedValues() {
        return allowedValues;
    }

    public Predicate<Object> getCustomValidator() {
        return customValidator;
    }

    public GenericContextualValidator<T> getContextualValidator() {
        return contextualValidator;
    }

    public String getErrorMessage() {
        return errorMessage;
    }
}
