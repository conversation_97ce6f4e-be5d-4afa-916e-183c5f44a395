package com.arihantcapital.oneclick.validation.generic;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

public class ValidationConfigurationRegistry {
    private final Map<Class<?>, Map<String, GenericFieldValidationConfig<?>>> configurations =
            new ConcurrentHashMap<>();

    @SuppressWarnings("unchecked")
    public <T> Map<String, GenericFieldValidationConfig<T>> getConfiguration(Class<T> clazz) {
        return (Map<String, GenericFieldValidationConfig<T>>) configurations.get(clazz);
    }

    public <T> void registerConfiguration(Class<T> clazz,
                                          Map<String, GenericFieldValidationConfig<T>> config) {
        configurations.put(clazz, (Map<String, GenericFieldValidationConfig<?>>) config);
    }

    public boolean hasConfiguration(Class<?> clazz) {
        return configurations.containsKey(clazz);
    }
}
