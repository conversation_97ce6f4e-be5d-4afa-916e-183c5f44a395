package com.arihantcapital.oneclick.validation.generic;

import com.arihantcapital.oneclick.enums.ServiceType;
import com.arihantcapital.oneclick.service.GenericValidationService;
import com.arihantcapital.oneclick.validation.CommonData;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

@SpringBootTest
public class GenericValidationTest {
    @Autowired
    private GenericValidationService validationService;

    @Test
    public void testUserProfileValidation() {
        UserProfile profile = new UserProfile();
        profile.setUsername("john_doe");
        profile.setAge(25);
        profile.setProfileType("STANDARD");
        profile.setBio("This is a short bio");

        GenericValidationService.ValidationResult result =
                validationService.validateForService(profile, ServiceType.USER_SERVICE);

        assertTrue(result.isValid(), "Valid profile should pass validation");
    }

    @Test
    public void testBusinessProfileRequiresSSN() {
        UserProfile profile = new UserProfile();
        profile.setUsername("business_user");
        profile.setAge(30);
        profile.setProfileType("BUSINESS");
        // No SSN provided

        GenericValidationService.ValidationResult result =
                validationService.validateForService(profile, ServiceType.USER_SERVICE);

        assertFalse(result.isValid(), "Business profile should require SSN");
        assertTrue(result.getErrors().containsKey("socialSecurityNumber"));
    }

    @Test
    public void testAgeRestrictedLanguage() {
        UserProfile profile = new UserProfile();
        profile.setUsername("young_user");
        profile.setAge(16);
        profile.setPreferredLanguage("ADULT_CONTENT_LANG");

        GenericValidationService.ValidationResult result =
                validationService.validateForService(profile, ServiceType.USER_SERVICE);

        assertFalse(result.isValid(), "Minors should not access adult content language");
        assertTrue(result.getErrors().containsKey("preferredLanguage"));
    }

    @Test
    public void testCommonDataStillWorks() {
        CommonData paymentData = new CommonData();
        paymentData.setAmount(100.0);
        paymentData.setPaymentMethod("CREDIT_CARD");
        paymentData.setCurrency("USD");
        paymentData.setAddress("123 Main St");

        GenericValidationService.ValidationResult result =
                validationService.validateForService(paymentData, ServiceType.PAYMENT_SERVICE);

        assertTrue(result.isValid(), "CommonData validation should still work");
    }
}
