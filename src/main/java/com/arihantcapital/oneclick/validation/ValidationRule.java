package com.arihantcapital.oneclick.validation;

import java.util.List;
import java.util.function.Predicate;

public class ValidationRule {
    private boolean required = false;
    private Integer minLength;
    private Integer maxLength;
    private String regexPattern;
    private List<Object> allowedValues;
    private Predicate<Object> customValidator;
    private String errorMessage;

    // Constructors
    public ValidationRule() {
    }

    public ValidationRule(boolean required) {
        this.required = required;
    }

    // Builder pattern methods
    public ValidationRule required(boolean required) {
        this.required = required;
        return this;
    }

    public ValidationRule minLength(Integer minLength) {
        this.minLength = minLength;
        return this;
    }

    public ValidationRule maxLength(Integer maxLength) {
        this.maxLength = maxLength;
        return this;
    }

    public ValidationRule regexPattern(String regexPattern) {
        this.regexPattern = regexPattern;
        return this;
    }

    public ValidationRule allowedValues(List<Object> allowedValues) {
        this.allowedValues = allowedValues;
        return this;
    }

    public ValidationRule customValidator(Predicate<Object> customValidator) {
        this.customValidator = customValidator;
        return this;
    }

    public ValidationRule errorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
        return this;
    }

    // Getters
    public boolean isRequired() {
        return required;
    }

    public Integer getMinLength() {
        return minLength;
    }

    public Integer getMaxLength() {
        return maxLength;
    }

    public String getRegexPattern() {
        return regexPattern;
    }

    public List<Object> getAllowedValues() {
        return allowedValues;
    }

    public Predicate<Object> getCustomValidator() {
        return customValidator;
    }

    public String getErrorMessage() {
        return errorMessage;
    }
}
