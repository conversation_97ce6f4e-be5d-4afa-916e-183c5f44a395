package com.arihantcapital.oneclick.validation.context;

public class ConditionalValidationRule extends ValidationRule {
    private ContextualValidator condition;

    public ConditionalValidationRule when(ContextualValidator condition) {
        this.condition = condition;
        return this;
    }

    public boolean shouldApply(ValidationContext context) {
        return condition == null || condition.validate(context);
    }

    public ContextualValidator getCondition() {
        return condition;
    }
}
