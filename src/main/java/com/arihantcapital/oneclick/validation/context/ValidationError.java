package com.arihantcapital.oneclick.validation.context;

public class ValidationError extends RuntimeException {
    private final String field;
    private final String message;

    public ValidationError(String field, String message) {
        super(String.format("Validation failed for field '%s': %s", field, message));
        this.field = field;
        this.message = message;
    }

    public String getField() {
        return field;
    }

    public String getValidationMessage() {
        return message;
    }
}
