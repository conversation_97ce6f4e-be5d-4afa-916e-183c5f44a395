package com.arihantcapital.oneclick.validation.context;

import com.arihantcapital.oneclick.enums.ServiceType;
import com.arihantcapital.oneclick.validation.CommonData;
import lombok.Data;

import java.util.*;
import java.util.regex.Pattern;

@Data
public class DataValidator {
    private final Map<String, FieldValidationConfig> fieldConfigs;

    public DataValidator() {
        this.fieldConfigs = new HashMap<>();
        setupValidationRules();
    }

    private void setupValidationRules() {
        // User ID validation
        fieldConfigs.put("userId", new FieldValidationConfig("userId")
                .addRule(ServiceType.USER_SERVICE, new ValidationRule()
                        .required(true)
                        .minLength(5)
                        .maxLength(20)
                        .regexPattern("^[a-zA-Z0-9_]+$")
                        .errorMessage("User ID must be 5-20 alphanumeric characters"))
                .addRule(ServiceType.ORDER_SERVICE, new ValidationRule()
                        .required(true)
                        .minLength(5)
                        .errorMessage("User ID is required for orders")));

        // Email validation with context-dependent rules
        fieldConfigs.put("email", new FieldValidationConfig("email")
                .addRule(ServiceType.USER_SERVICE, new ValidationRule()
                        .required(true)
                        .regexPattern("^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$")
                        .errorMessage("Valid email address is required"))
                .addRule(ServiceType.NOTIFICATION_SERVICE, new ValidationRule()
                        .required(true)
                        .regexPattern("^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$")
                        .contextualValidator(context -> {
                            // Email is required only if phone is not provided
                            return context.hasValue("email") || context.hasValue("phone");
                        })
                        .errorMessage("Either email or phone is required for notifications")));

        // Phone validation with conditional requirements
        ConditionalValidationRule phoneRule = (ConditionalValidationRule) new ConditionalValidationRule()
                .when(context -> "SMS".equals(context.getStringValue("notificationMethod")) ||
                        !context.hasValue("email"))
                .required(true)
                .regexPattern("^\\+?[\\d\\s\\-\\(\\)]{10,15}$")
                .errorMessage("Valid phone number required for SMS notifications or when email not provided");

        fieldConfigs.put("phone", new FieldValidationConfig("phone")
                .addRule(ServiceType.USER_SERVICE, new ValidationRule()
                        .required(false)
                        .regexPattern("^\\+?[\\d\\s\\-\\(\\)]{10,15}$")
                        .errorMessage("Phone must be 10-15 digits with optional formatting"))
                .addRule(ServiceType.NOTIFICATION_SERVICE, phoneRule));

        // Name validation
        fieldConfigs.put("name", new FieldValidationConfig("name")
                .addRule(ServiceType.USER_SERVICE, new ValidationRule()
                        .required(true)
                        .minLength(2)
                        .maxLength(50)
                        .errorMessage("Name must be 2-50 characters"))
                .addRule(ServiceType.ORDER_SERVICE, new ValidationRule()
                        .required(true)
                        .minLength(2)
                        .errorMessage("Customer name required for orders")));

        // Amount validation with context-dependent limits
        fieldConfigs.put("amount", new FieldValidationConfig("amount")
                .addRule(ServiceType.ORDER_SERVICE, new ValidationRule()
                        .required(true)
                        .customValidator(value -> value != null && ((Double) value) > 0)
                        .errorMessage("Amount must be greater than 0"))
                .addRule(ServiceType.PAYMENT_SERVICE, new ValidationRule()
                        .required(true)
                        .contextualValidator(context -> {
                            Double amount = context.getDoubleValue("amount");
                            Boolean isVip = context.getBooleanValue("isVipCustomer");
                            String paymentMethod = context.getStringValue("paymentMethod");

                            if (amount == null || amount <= 0) return false;

                            // VIP customers have higher limits
                            double maxLimit = Boolean.TRUE.equals(isVip) ? 50000 : 10000;

                            // Credit card has different limits than debit
                            if ("CREDIT_CARD".equals(paymentMethod)) {
                                maxLimit *= 1.5; // 50% higher limit for credit cards
                            }

                            return amount <= maxLimit;
                        })
                        .errorMessage("Amount exceeds allowed limit based on customer type and payment method")));

        // Currency validation with country context
        fieldConfigs.put("currency", new FieldValidationConfig("currency")
                .addRule(ServiceType.ORDER_SERVICE, new ValidationRule()
                        .required(true)
                        .contextualValidator(context -> {
                            String currency = context.getStringValue("currency");
                            String country = context.getStringValue("country");

                            if (currency == null) return false;

                            // Country-specific currency validation
                            Map<String, List<String>> countryCurrencies = Map.of(
                                    "US", Arrays.asList("USD"),
                                    "UK", Arrays.asList("GBP", "USD", "EUR"),
                                    "IN", Arrays.asList("INR", "USD"),
                                    "DE", Arrays.asList("EUR", "USD")
                            );

                            if (country != null && countryCurrencies.containsKey(country)) {
                                return countryCurrencies.get(country).contains(currency);
                            }

                            // Default allowed currencies
                            return Arrays.asList("USD", "EUR", "GBP", "INR").contains(currency);
                        })
                        .errorMessage("Currency not supported for the specified country"))
                .addRule(ServiceType.PAYMENT_SERVICE, new ValidationRule()
                        .required(true)
                        .allowedValues(Arrays.asList("USD", "EUR", "GBP", "INR"))
                        .errorMessage("Valid currency required for payment")));

        // Payment method validation with amount context
        fieldConfigs.put("paymentMethod", new FieldValidationConfig("paymentMethod")
                .addRule(ServiceType.PAYMENT_SERVICE, new ValidationRule()
                        .required(true)
                        .contextualValidator(context -> {
                            String paymentMethod = context.getStringValue("paymentMethod");
                            Double amount = context.getDoubleValue("amount");
                            String accountType = context.getStringValue("accountType");

                            if (paymentMethod == null) return false;

                            List<String> validMethods = Arrays.asList("CREDIT_CARD", "DEBIT_CARD", "BANK_TRANSFER", "DIGITAL_WALLET");
                            if (!validMethods.contains(paymentMethod)) return false;

                            // High amounts require secure payment methods
                            if (amount != null && amount > 5000) {
                                return Arrays.asList("CREDIT_CARD", "BANK_TRANSFER").contains(paymentMethod);
                            }

                            // Business accounts have different payment method restrictions
                            if ("BUSINESS".equals(accountType)) {
                                return !paymentMethod.equals("DIGITAL_WALLET");
                            }

                            return true;
                        })
                        .errorMessage("Payment method not allowed for the given amount or account type")));

        // Address validation - required for certain payment methods
        fieldConfigs.put("address", new FieldValidationConfig("address")
                .addRule(ServiceType.PAYMENT_SERVICE, new ConditionalValidationRule()
                        .when(context -> {
                            String paymentMethod = context.getStringValue("paymentMethod");
                            return "CREDIT_CARD".equals(paymentMethod) || "DEBIT_CARD".equals(paymentMethod);
                        })
                        .required(true)
                        .minLength(10)
                        .errorMessage("Address is required for card payments and must be at least 10 characters")));

        // Status validation
        fieldConfigs.put("status", new FieldValidationConfig("status")
                .addRule(ServiceType.ORDER_SERVICE, new ValidationRule()
                        .allowedValues(Arrays.asList("pending", "processing", "shipped", "delivered", "cancelled"))
                        .errorMessage("Invalid order status"))
                .addRule(ServiceType.PAYMENT_SERVICE, new ValidationRule()
                        .allowedValues(Arrays.asList("pending", "completed", "failed", "refunded"))
                        .errorMessage("Invalid payment status")));
    }

    private List<String> validateField(String fieldName, Object value, ValidationRule rule, ValidationContext context) {
        List<String> errors = new ArrayList<>();

        // Check if this is a conditional rule and if it should be applied
        if (rule instanceof ConditionalValidationRule) {
            ConditionalValidationRule conditionalRule = (ConditionalValidationRule) rule;
            if (!conditionalRule.shouldApply(context)) {
                return errors; // Skip validation if condition not met
            }
        }

        // Check contextual validator first (it has access to all context)
        if (rule.getContextualValidator() != null && !rule.getContextualValidator().validate(context)) {
            errors.add(rule.getErrorMessage() != null ? rule.getErrorMessage() :
                    fieldName + " failed contextual validation");
            return errors; // Return early if contextual validation fails
        }

        // Check if field is required
        if (rule.isRequired() && (value == null || value.toString().trim().isEmpty())) {
            errors.add(rule.getErrorMessage() != null ? rule.getErrorMessage() : fieldName + " is required");
            return errors;
        }

        // Skip other validations if value is null/empty and not required
        if (value == null || value.toString().trim().isEmpty()) {
            return errors;
        }

        String stringValue = value.toString();

        // Length validations
        if (rule.getMinLength() != null && stringValue.length() < rule.getMinLength()) {
            errors.add(rule.getErrorMessage() != null ? rule.getErrorMessage() :
                    fieldName + " must be at least " + rule.getMinLength() + " characters");
        }

        if (rule.getMaxLength() != null && stringValue.length() > rule.getMaxLength()) {
            errors.add(rule.getErrorMessage() != null ? rule.getErrorMessage() :
                    fieldName + " must be at most " + rule.getMaxLength() + " characters");
        }

        // Regex validation
        if (rule.getRegexPattern() != null && !Pattern.matches(rule.getRegexPattern(), stringValue)) {
            errors.add(rule.getErrorMessage() != null ? rule.getErrorMessage() :
                    fieldName + " format is invalid");
        }

        // Allowed values validation
        if (rule.getAllowedValues() != null && !rule.getAllowedValues().contains(value)) {
            errors.add(rule.getErrorMessage() != null ? rule.getErrorMessage() :
                    fieldName + " must be one of: " + rule.getAllowedValues());
        }

        // Custom validator (for single field validation)
        if (rule.getCustomValidator() != null && !rule.getCustomValidator().test(value)) {
            errors.add(rule.getErrorMessage() != null ? rule.getErrorMessage() :
                    fieldName + " failed custom validation");
        }

        return errors;
    }

    public Map<String, List<String>> validate(CommonData data, ServiceType serviceType) {
        Map<String, List<String>> validationErrors = new HashMap<>();
        ValidationContext context = new ValidationContext(data, serviceType);

        for (Map.Entry<String, FieldValidationConfig> entry : fieldConfigs.entrySet()) {
            String fieldName = entry.getKey();
            FieldValidationConfig config = entry.getValue();
            ValidationRule rule = config.getRule(serviceType);

            if (rule != null) { // Only validate if service has rules for this field
                Object value = context.getFieldValue(fieldName);
                List<String> fieldErrors = validateField(fieldName, value, rule, context);
                if (!fieldErrors.isEmpty()) {
                    validationErrors.put(fieldName, fieldErrors);
                }
            }
        }

        return validationErrors;
    }

    public void validateAndThrow(CommonData data, ServiceType serviceType) throws ValidationError {
        Map<String, List<String>> errors = validate(data, serviceType);
        if (!errors.isEmpty()) {
            // Throw the first error found
            Map.Entry<String, List<String>> firstError = errors.entrySet().iterator().next();
            throw new ValidationError(firstError.getKey(), firstError.getValue().get(0));
        }
    }

    public boolean isValid(CommonData data, ServiceType serviceType) {
        return validate(data, serviceType).isEmpty();
    }
}
